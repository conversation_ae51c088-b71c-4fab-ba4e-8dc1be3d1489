@echo off
echo ========================================
echo TProxy Debug Mode - IPv6 ROUTING FIX
echo ========================================
echo.
echo CRITICAL DISCOVERY:
echo - IPv6 source address is correct in our packets: fe80::1234:5678:9abc:def0
echo - BUT router responds to: fe80::16d5:6ea1:e8d6:3fd2 (system's real IPv6)
echo - This means system is changing source address during routing!
echo.
echo SOLUTION:
echo 1. First run: announce_ipv6.bat (as Administrator)
echo    - This sets up proper IPv6 routing via WinTun interface
echo 2. Then run this script
echo.
echo EXPECTED FLOW:
echo 1. OUTBOUND: IPv4 -> IPv6 (working)
echo    * Source: fe80::1234:5678:9abc:def0 (WinTun IPv6)
echo    * Dest:   fe80::ca75:f4ff:fe67:c094 (router IPv6)
echo.
echo 2. RETURN: IPv6 -> IPv4 (should work after routing fix)
echo    * Router should respond to: fe80::1234:5678:9abc:def0
echo    * Not to: fe80::16d5:6ea1:e8d6:3fd2
echo.
echo DEBUG INFO TO WATCH:
echo    * "IPv6 packet interface: XX, outbound: true"
echo    * "Processing IPv6 return traffic: fe80::ca75:f4ff:fe67:c094 -> fe80::1234:5678:9abc:def0"
echo.
echo IMPORTANT: Run announce_ipv6.bat FIRST to fix IPv6 routing!
echo.
echo Press any key to start TProxy...
pause > nul

set RUST_LOG=debug
powershell -Command "Start-Process -FilePath '.\target\release\tproxy.exe' -Verb RunAs -Wait"
