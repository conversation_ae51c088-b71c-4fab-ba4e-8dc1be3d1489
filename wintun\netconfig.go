//go:build windows

package wintun

import (
	"fmt"
	"net"
	"os/exec"
	"strings"
	"unsafe"

	"golang.org/x/sys/windows"
)

// Windows API constants for network configuration
const (
	// IP Helper API constants
	NO_ERROR                    = 0
	ERROR_BUFFER_OVERFLOW       = 111
	ERROR_INSUFFICIENT_BUFFER   = 122
	ERROR_INVALID_PARAMETER     = 87
	ERROR_NOT_FOUND             = 1168
	ERROR_OBJECT_ALREADY_EXISTS = 5010

	// Interface types
	IF_TYPE_TUNNEL = 131

	// IP address types
	IP_ADAPTER_UNICAST_ADDRESS_DNS_ELIGIBLE = 0x01
)

// Windows API structures
type MIB_UNICASTIPADDRESS_ROW struct {
	Address            windows.RawSockaddrInet4
	InterfaceLuid      uint64
	InterfaceIndex     uint32
	PrefixOrigin       uint32
	SuffixOrigin       uint32
	ValidLifetime      uint32
	PreferredLifetime  uint32
	OnLinkPrefixLength uint8
	SkipAsSource       uint8
	DadState           uint32
	ScopeId            uint32
	CreationTimeStamp  uint64
}

// Windows API functions
var (
	iphlpapi = windows.NewLazySystemDLL("iphlpapi.dll")

	procCreateUnicastIpAddressEntry = iphlpapi.NewProc("CreateUnicastIpAddressEntry")
	procDeleteUnicastIpAddressEntry = iphlpapi.NewProc("DeleteUnicastIpAddressEntry")
	procGetUnicastIpAddressEntry    = iphlpapi.NewProc("GetUnicastIpAddressEntry")
)

// ConfigureInterfaceIP configures the IP address for a network interface
func ConfigureInterfaceIP(luid uint64, ipAddr net.IP, prefixLength uint8) error {
	if ipAddr.To4() == nil {
		return fmt.Errorf("only IPv4 addresses are supported")
	}

	// Try Windows API first
	err := configureInterfaceIPAPI(luid, ipAddr, prefixLength)
	if err == nil {
		return nil
	}

	// If API fails, try netsh command as fallback
	return configureInterfaceIPNetsh(luid, ipAddr, prefixLength)
}

// configureInterfaceIPAPI configures IP using Windows API
func configureInterfaceIPAPI(luid uint64, ipAddr net.IP, prefixLength uint8) error {
	// Create the IP address row structure
	var row MIB_UNICASTIPADDRESS_ROW
	row.InterfaceLuid = luid
	row.OnLinkPrefixLength = prefixLength
	row.DadState = 4 // IpDadStatePreferred
	row.ValidLifetime = 0xffffffff
	row.PreferredLifetime = 0xffffffff

	// Set the IPv4 address
	sockaddr := (*windows.RawSockaddrInet4)(unsafe.Pointer(&row.Address))
	sockaddr.Family = windows.AF_INET
	copy(sockaddr.Addr[:], ipAddr.To4())

	// Call the Windows API
	ret, _, err := procCreateUnicastIpAddressEntry.Call(uintptr(unsafe.Pointer(&row)))
	if ret != NO_ERROR {
		if ret == ERROR_OBJECT_ALREADY_EXISTS {
			// Address already exists, which is fine
			return nil
		}
		return fmt.Errorf("CreateUnicastIpAddressEntry failed: %v (code: %d)", err, ret)
	}

	return nil
}

// configureInterfaceIPNetsh configures IP using netsh command
func configureInterfaceIPNetsh(luid uint64, ipAddr net.IP, prefixLength uint8) error {
	// First, we need to find the interface name from LUID
	interfaceName, err := getInterfaceNameFromLUID(luid)
	if err != nil {
		return fmt.Errorf("failed to get interface name: %w", err)
	}

	// Build netsh command
	// netsh interface ip set address "interface name" static IP mask
	cmd := exec.Command("netsh", "interface", "ip", "set", "address",
		interfaceName, "static", ipAddr.String(), "*************")

	output, err := cmd.CombinedOutput()
	if err != nil {
		return fmt.Errorf("netsh command failed: %v, output: %s", err, string(output))
	}

	return nil
}

// getInterfaceNameFromLUID gets interface name from LUID
func getInterfaceNameFromLUID(luid uint64) (string, error) {
	// This is a simplified implementation
	// In practice, we would use ConvertInterfaceLuidToNameA or similar API

	// For now, try to find the interface by checking all interfaces
	interfaces, err := net.Interfaces()
	if err != nil {
		return "", err
	}

	// Look for interfaces that might match our LUID
	// This is not perfect but should work for most cases
	for _, iface := range interfaces {
		if strings.Contains(strings.ToLower(iface.Name), "wintun") ||
			strings.Contains(strings.ToLower(iface.Name), "nat46") ||
			strings.Contains(strings.ToLower(iface.Name), "test") {
			return iface.Name, nil
		}
	}

	return "", fmt.Errorf("interface not found for LUID %d", luid)
}

// RemoveInterfaceIP removes an IP address from a network interface
func RemoveInterfaceIP(luid uint64, ipAddr net.IP) error {
	if ipAddr.To4() == nil {
		return fmt.Errorf("only IPv4 addresses are supported")
	}

	// Create the IP address row structure for deletion
	var row MIB_UNICASTIPADDRESS_ROW
	row.InterfaceLuid = luid

	// Set the IPv4 address
	sockaddr := (*windows.RawSockaddrInet4)(unsafe.Pointer(&row.Address))
	sockaddr.Family = windows.AF_INET
	copy(sockaddr.Addr[:], ipAddr.To4())

	// First, get the existing entry to fill in required fields
	ret, _, err := procGetUnicastIpAddressEntry.Call(uintptr(unsafe.Pointer(&row)))
	if ret != NO_ERROR {
		if ret == ERROR_NOT_FOUND {
			// Address doesn't exist, which is fine
			return nil
		}
		return fmt.Errorf("GetUnicastIpAddressEntry failed: %v (code: %d)", err, ret)
	}

	// Now delete the entry
	ret, _, err = procDeleteUnicastIpAddressEntry.Call(uintptr(unsafe.Pointer(&row)))
	if ret != NO_ERROR {
		if ret == ERROR_NOT_FOUND {
			// Address doesn't exist, which is fine
			return nil
		}
		return fmt.Errorf("DeleteUnicastIpAddressEntry failed: %v (code: %d)", err, ret)
	}

	return nil
}

// SetInterfaceUp brings a network interface up
func SetInterfaceUp(luid uint64) error {
	// This is a simplified implementation
	// In practice, you might need to use additional Windows APIs
	// to properly bring the interface up
	return nil
}

// SetInterfaceDown brings a network interface down
func SetInterfaceDown(luid uint64) error {
	// This is a simplified implementation
	// In practice, you might need to use additional Windows APIs
	// to properly bring the interface down
	return nil
}

// GetInterfaceIndex gets the interface index from LUID
func GetInterfaceIndex(luid uint64) (uint32, error) {
	// This would typically use ConvertInterfaceLuidToIndex
	// For now, we'll return a placeholder
	return 0, fmt.Errorf("GetInterfaceIndex not implemented")
}

// Helper function to convert IP address to string
func ipToString(ip net.IP) string {
	if ip == nil {
		return ""
	}
	return ip.String()
}

// Helper function to calculate subnet mask from prefix length
func prefixLengthToMask(prefixLength uint8) net.IPMask {
	if prefixLength > 32 {
		prefixLength = 32
	}

	mask := net.CIDRMask(int(prefixLength), 32)
	return mask
}

// ValidateIPConfiguration validates IP configuration parameters
func ValidateIPConfiguration(ipAddr net.IP, prefixLength uint8) error {
	if ipAddr == nil {
		return fmt.Errorf("IP address cannot be nil")
	}

	if ipAddr.To4() == nil {
		return fmt.Errorf("only IPv4 addresses are supported")
	}

	if prefixLength > 32 {
		return fmt.Errorf("prefix length cannot be greater than 32 for IPv4")
	}

	if prefixLength == 0 {
		return fmt.Errorf("prefix length cannot be 0")
	}

	return nil
}
