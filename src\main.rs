use log::{info, error, debug, warn};
use std::collections::HashMap;
use std::net::{Ipv4Addr, Ipv6Addr, IpAddr};
use std::sync::{Arc, Mutex};
use std::time::{Duration, Instant};
use windivert::prelude::*;
use wintun::Adapter;
use std::process::Command;
use etherparse::{IpHeader, TransportHeader};

// 常量定义
const WINTUN_ADAPTER_NAME: &str = "TProxy-Wintun";
const WINTUN_ADAPTER_IP: &str = "*************";
const WINTUN_ADAPTER_MASK: &str = "*************";
const WINTUN_ADAPTER_IPV6: &str = "fd00::1";
const CONNECTION_TIMEOUT: Duration = Duration::from_secs(300); // 5 minutes

// TCP connection states
#[derive(Debug, <PERSON><PERSON>, PartialEq)]
enum TcpState {
    SynS<PERSON>,
    SynR<PERSON>eived,
    Established,
    FinWait1,
    <PERSON>Wait2,
    CloseWait,
    Closing,
    LastAck,
    TimeWait,
    Closed,
}

// Connection tracking entry
#[derive(Debug, Clone)]
struct ConnectionEntry {
    // Original IPv4 connection
    ipv4_src: Ipv4Addr,
    ipv4_dst: Ipv4Addr,
    ipv4_src_port: u16,
    ipv4_dst_port: u16,

    // Mapped IPv6 connection
    ipv6_src: Ipv6Addr,
    ipv6_dst: Ipv6Addr,
    ipv6_src_port: u16,
    ipv6_dst_port: u16,

    // Connection state
    tcp_state: TcpState,
    last_activity: Instant,

    // Sequence number tracking for TCP
    seq_offset: u32,
    ack_offset: u32,
}

// Connection key for tracking
#[derive(Debug, Clone, Hash, PartialEq, Eq)]
struct ConnectionKey {
    src_ip: IpAddr,
    dst_ip: IpAddr,
    src_port: u16,
    dst_port: u16,
    protocol: u8,
}

// Connection tracker
type ConnectionTracker = Arc<Mutex<HashMap<ConnectionKey, ConnectionEntry>>>;

fn main() {
    env_logger::init();

    // Load configuration
    let config = load_config();
    let ipv4_to_ipv6_map = load_ipv4_to_ipv6_mapping();

    // Initialize connection tracker
    let connection_tracker: ConnectionTracker = Arc::new(Mutex::new(HashMap::new()));

    // Parse traffic filters
    let traffic_filters = parse_traffic_filters(&config.traffic_filters);
    
    // --- Wintun 初始化 ---
    info!("Attempting to create Wintun adapter...");
    
    // 加载 wintun.dll
    let wintun = unsafe { wintun::load() }.expect("Failed to load wintun.dll");
    info!("Successfully loaded wintun.dll");

    // 尝试打开一个现有的适配器，如果不存在则创建一个新的
    let adapter = match Adapter::open(&wintun, WINTUN_ADAPTER_NAME) {
        Ok(a) => {
            info!("Opened existing Wintun adapter.");
            a
        }
        Err(e) => {
            info!("Failed to open existing Wintun adapter: {}", e);
            info!("Creating new Wintun adapter.");
            match Adapter::create(&wintun, WINTUN_ADAPTER_NAME, "Wintun Adapter for TProxy", None) {
                Ok(adapter) => {
                    info!("Wintun adapter created.");
                    adapter
                }
                Err(e) => {
                    error!("Failed to create Wintun adapter: {}. Please ensure wintun.dll is available and you are running as Administrator.", e);
                    return;
                }
            }
        }
    };

    let if_idx = match adapter.get_adapter_index() {
        Ok(idx) => {
            info!("Wintun adapter index: {}", idx);
            idx
        }
        Err(e) => {
            error!("Failed to get Wintun adapter index: {}", e);
            return;
        }
    };

    // --- 为 Wintun 网卡配置 IP 地址 ---
    info!("Configuring IP address for Wintun adapter...");
    let ip_addr: std::net::Ipv4Addr = WINTUN_ADAPTER_IP.parse().expect("Invalid IP address ");
    let netmask: std::net::Ipv4Addr = WINTUN_ADAPTER_MASK.parse().expect("Invalid netmask ");
    let ipv6_addr: std::net::Ipv6Addr = WINTUN_ADAPTER_IPV6.parse().expect("Invalid IPv6 address");


    match adapter.set_network_addresses_tuple(
        std::net::IpAddr::V4(ip_addr),
        std::net::IpAddr::V4(netmask),
        None // No gateway needed for this setup
    ) {
        Ok(_) => info!("Successfully set Wintun IP to {}", WINTUN_ADAPTER_IP),
        Err(e) => {
            error!("Failed to set Wintun IP and IPv6 address: {}. Make sure you are running as Administrator.", e);
            return;
        }
    }

    // 设置 IPv6 地址
    let output = Command::new("netsh")
        .args(&[
            "interface", "ipv6", "add", "address",
            &format!("interface={}", if_idx), // 使用接口索引
            &format!("address={}", WINTUN_ADAPTER_IPV6), // IPv6 地址
            "store=active"
        ])
        .output();

    match output {
        Ok(o) => {
            if o.status.success() {
                info!("Successfully set IPv6 address {} on interface {}", WINTUN_ADAPTER_IPV6, if_idx);
            } else {
                error!(
                    "Failed to set IPv6 address: {}",
                    String::from_utf8_lossy(&o.stderr)
                );
            }
        }
        Err(e) => {
            error!("Failed to execute netsh command: {}", e);
        }
    }

    // --- 启动 Wintun 会话 ---
    info!("Starting Wintun session...");
    let _session = match adapter.start_session(wintun::MAX_RING_CAPACITY) {
        Ok(session) => {
            info!("Wintun session started successfully.");
            session
        }
        Err(e) => {
            error!("Failed to start Wintun session: {}", e);
            return;
        }
    };

    // --- WinDivert 初始化 ---
    // 捕获WinTun接口的所有流量 + 所有IPv6流量（用于返回流量）
    // 这样可以捕获出站的IPv4流量和来自任何接口的IPv6返回流量
    let filter = format!("(ifIdx == {}) or (ipv6 and inbound)", if_idx);
    info!("Using WinDivert filter: {}", filter);
    
    let divert = match WinDivert::network(
        &filter,
        0, // priority
        WinDivertFlags::new() // 默认为拦截模式
    ) {
        Ok(handle) => handle,
        Err(e) => {
            error!("Failed to open WinDivert handle: {}", e);
            return;
        }
    };

    info!("WinDivert handle opened successfully. Capturing packets on Wintun adapter...");

    // 创建足够大的缓冲区来处理最大可能的数据包
    let mut buffer = vec![0u8; 65536]; // 64KB 缓冲区
    
    loop {
        // Clean up expired connections
        cleanup_expired_connections(&connection_tracker);

        match divert.recv(Some(&mut buffer)) {
            Ok(mut packet) => {
                let is_outbound = packet.address.outbound();
                info!("Captured a {} packet with length: {} on interface {}",
                      if is_outbound { "outbound" } else { "inbound" },
                      packet.data.len(),
                      packet.address.interface_index());

                // Log detailed packet information
                debug!("Packet address: {:?}", packet.address);
                debug!("Packet data (first 64 bytes): {:?}", &packet.data[..std::cmp::min(64, packet.data.len())]);

                // Parse the packet - WinDivert captures IP packets without Ethernet headers
                let mut should_resend = true;

                // Try to parse as IP packet directly (no Ethernet header)
                match etherparse::PacketHeaders::from_ip_slice(&packet.data) {
                    Ok(headers) => {
                        debug!("IP header: {:?}", headers.ip);
                        debug!("Transport header: {:?}", headers.transport);
                        debug!("Payload length: {}", headers.payload.len());

                        // Check if this packet needs processing
                        let needs_processing = if let Some(ref ip_header) = headers.ip {
                            match ip_header {
                                IpHeader::Version4(ipv4_header, _) => {
                                    let src_ipv4 = Ipv4Addr::from(ipv4_header.source);
                                    let dst_ipv4 = Ipv4Addr::from(ipv4_header.destination);

                                    info!("IPv4 packet: {} -> {} (protocol: {})", src_ipv4, dst_ipv4, ipv4_header.protocol);

                                    // Check if either address is in our mapping
                                    ipv4_to_ipv6_map.contains_key(&src_ipv4) ||
                                    ipv4_to_ipv6_map.contains_key(&dst_ipv4)
                                }
                                IpHeader::Version6(ipv6_header, _) => {
                                    let src_ipv6 = Ipv6Addr::from(ipv6_header.source);
                                    let dst_ipv6 = Ipv6Addr::from(ipv6_header.destination);

                                    info!("IPv6 packet: {} -> {}", src_ipv6, dst_ipv6);
                                    true // Always process IPv6 packets (potential return traffic)
                                }
                            }
                        } else {
                            false
                        };

                        if needs_processing {
                            // Try to process the packet with connection tracking
                            if let Some(processed_packet) = process_packet_with_tracking(
                                &packet.data,
                                &headers,
                                &ipv4_to_ipv6_map,
                                &connection_tracker,
                                is_outbound
                            ) {
                                packet.data = processed_packet.into();
                                info!("Packet processed and converted successfully");
                            } else {
                                info!("Packet forwarded without conversion");
                            }
                        } else {
                            // Not our target traffic, just forward it
                            debug!("Packet not in scope, forwarding as-is");
                        }
                    }
                    Err(e) => {
                        debug!("Failed to parse packet: {}", e);
                        // For unparseable packets, just forward them
                        debug!("Forwarding unparseable packet as-is");
                    }
                }

                // 将处理后的数据包重新注入或转发
                if should_resend {
                    // 检查是否是转换后的IPv6数据包
                    if packet.data.len() > 0 && (packet.data[0] >> 4) == 6 {
                        // 这是IPv6数据包，需要通过系统路由转发
                        info!("Forwarding converted IPv6 packet to system");
                        debug!("IPv6 packet interface: {}, outbound: {}",
                               packet.address.interface_index(),
                               packet.address.outbound());

                        // 确保IPv6数据包通过正确的接口发送
                        // 保持原始的outbound状态，让系统正确路由
                    }

                    match divert.send(&packet) {
                        Ok(_) => debug!("Packet forwarded successfully"),
                        Err(e) => error!("Failed to forward packet: {}", e)
                    }
                } else {
                    debug!("Packet dropped - not forwarding");
                }
            }
            Err(e) => {
                error!("Failed to receive packet: {}", e);
                break;
            }
        }
    }
    
    info!("Closing WinDivert handle.");
}

// Enhanced configuration structure
#[derive(serde::Deserialize, Debug, Clone)]
struct TProxyConfig {
    interfaces: InterfaceConfig,
    address_mappings: HashMap<String, String>,
    traffic_filters: TrafficFilters,
    connection_tracking: ConnectionTrackingConfig,
    logging: LoggingConfig,
}

#[derive(serde::Deserialize, Debug, Clone)]
struct InterfaceConfig {
    wintun: WintunConfig,
    tailscale: TailscaleConfig,
}

#[derive(serde::Deserialize, Debug, Clone)]
struct WintunConfig {
    name: String,
    ipv4_address: String,
    ipv4_netmask: String,
    ipv6_address: String,
}

#[derive(serde::Deserialize, Debug, Clone)]
struct TailscaleConfig {
    interface_index: Option<u32>,
}

#[derive(serde::Deserialize, Debug, Clone)]
struct TrafficFilters {
    ipv4_subnets: Vec<String>,
    ipv6_subnets: Vec<String>,
}

#[derive(serde::Deserialize, Debug, Clone)]
struct ConnectionTrackingConfig {
    timeout_seconds: u64,
    max_connections: usize,
    cleanup_interval_seconds: u64,
}

#[derive(serde::Deserialize, Debug, Clone)]
struct LoggingConfig {
    level: String,
    log_packets: bool,
    log_conversions: bool,
}

// Function to load configuration from config file
fn load_config() -> TProxyConfig {
    use std::fs::File;
    use std::io::Read;

    let mut file = File::open("config.yaml").expect("Failed to open config.yaml");
    let mut contents = String::new();
    file.read_to_string(&mut contents).expect("Failed to read config.yaml");

    let config: TProxyConfig = serde_yaml::from_str(&contents).expect("Failed to parse config.yaml");

    info!("Loaded configuration from config.yaml");
    debug!("Config: {:?}", config);

    config
}

// Function to load IPv4 to IPv6 mapping from config file
fn load_ipv4_to_ipv6_mapping() -> HashMap<std::net::Ipv4Addr, Ipv6Addr> {
    let config = load_config();

    let mut map = HashMap::new();
    for (ipv4_str, ipv6_str) in config.address_mappings {
        let ipv4: std::net::Ipv4Addr = ipv4_str.parse().expect("Invalid IPv4 address in config");
        let ipv6: Ipv6Addr = ipv6_str.parse().expect("Invalid IPv6 address in config");
        map.insert(ipv4, ipv6);
    }

    info!("Loaded {} IPv4 to IPv6 mappings from config.yaml", map.len());
    map
}

// Parse traffic filters from configuration
fn parse_traffic_filters(filters: &TrafficFilters) -> (Vec<ipnetwork::Ipv4Network>, Vec<ipnetwork::Ipv6Network>) {
    let mut ipv4_networks = Vec::new();
    let mut ipv6_networks = Vec::new();

    for subnet_str in &filters.ipv4_subnets {
        match subnet_str.parse::<ipnetwork::Ipv4Network>() {
            Ok(network) => ipv4_networks.push(network),
            Err(e) => error!("Invalid IPv4 subnet in config: {} - {}", subnet_str, e),
        }
    }

    for subnet_str in &filters.ipv6_subnets {
        match subnet_str.parse::<ipnetwork::Ipv6Network>() {
            Ok(network) => ipv6_networks.push(network),
            Err(e) => error!("Invalid IPv6 subnet in config: {} - {}", subnet_str, e),
        }
    }

    info!("Loaded {} IPv4 and {} IPv6 traffic filters", ipv4_networks.len(), ipv6_networks.len());
    (ipv4_networks, ipv6_networks)
}

// Check if an IPv4 address matches any configured filter
fn should_process_ipv4(ip: Ipv4Addr, filters: &[ipnetwork::Ipv4Network]) -> bool {
    if filters.is_empty() {
        return true; // No filters means process all
    }

    filters.iter().any(|network| network.contains(ip))
}

// Check if an IPv6 address matches any configured filter
fn should_process_ipv6(ip: Ipv6Addr, filters: &[ipnetwork::Ipv6Network]) -> bool {
    if filters.is_empty() {
        return true; // No filters means process all
    }

    filters.iter().any(|network| network.contains(ip))
}

// Clean up expired connections
fn cleanup_expired_connections(tracker: &ConnectionTracker) {
    let mut connections = tracker.lock().unwrap();
    let now = Instant::now();

    connections.retain(|_, entry| {
        now.duration_since(entry.last_activity) < CONNECTION_TIMEOUT
    });
}

// Process packet with connection tracking
fn process_packet_with_tracking(
    packet_data: &[u8],
    headers: &etherparse::PacketHeaders,
    ipv4_to_ipv6_map: &HashMap<Ipv4Addr, Ipv6Addr>,
    tracker: &ConnectionTracker,
    is_outbound: bool,
) -> Option<Vec<u8>> {
    if let Some(ref ip_header) = headers.ip {
        match ip_header {
            IpHeader::Version4(ipv4_header, _) => {
                let src_ipv4 = Ipv4Addr::from(ipv4_header.source);
                let dst_ipv4 = Ipv4Addr::from(ipv4_header.destination);

                // Check if we need to convert this packet
                if let Some(&mapped_ipv6) = ipv4_to_ipv6_map.get(&dst_ipv4) {
                    info!("Converting IPv4 packet {} -> {} to IPv6", src_ipv4, dst_ipv4);

                    // Extract transport layer information
                    if let Some(ref transport) = headers.transport {
                        match transport {
                            TransportHeader::Tcp(tcp_header) => {
                                return process_tcp_packet(
                                    packet_data, headers, ipv4_header, tcp_header,
                                    src_ipv4, dst_ipv4, ipv4_to_ipv6_map, tracker, is_outbound
                                );
                            }
                            TransportHeader::Udp(udp_header) => {
                                return process_udp_packet(
                                    packet_data, headers, ipv4_header, udp_header,
                                    src_ipv4, dst_ipv4, ipv4_to_ipv6_map, is_outbound
                                );
                            }
                            _ => {
                                debug!("Unsupported transport protocol for conversion");
                            }
                        }
                    } else {
                        // Handle other protocols (like ICMP) - for now just log
                        info!("Non-TCP/UDP packet to mapped address: {} -> {} (protocol: {})",
                              src_ipv4, dst_ipv4, ipv4_header.protocol);
                    }
                }
            }
            IpHeader::Version6(ipv6_header, _) => {
                // Handle IPv6 packets (return traffic)
                return process_ipv6_return_traffic(packet_data, headers, ipv6_header, tracker, is_outbound);
            }
        }
    }

    None
}

// Process TCP packets with connection tracking
fn process_tcp_packet(
    packet_data: &[u8],
    headers: &etherparse::PacketHeaders,
    ipv4_header: &etherparse::Ipv4Header,
    tcp_header: &etherparse::TcpHeader,
    src_ipv4: Ipv4Addr,
    dst_ipv4: Ipv4Addr,
    ipv4_to_ipv6_map: &HashMap<Ipv4Addr, Ipv6Addr>,
    tracker: &ConnectionTracker,
    is_outbound: bool,
) -> Option<Vec<u8>> {
    let src_port = tcp_header.source_port;
    let dst_port = tcp_header.destination_port;

    // Create connection key
    let conn_key = ConnectionKey {
        src_ip: IpAddr::V4(src_ipv4),
        dst_ip: IpAddr::V4(dst_ipv4),
        src_port,
        dst_port,
        protocol: ipv4_header.protocol,
    };

    // Check if destination needs to be converted (IPv4 -> IPv6)
    if let Some(&mapped_ipv6) = ipv4_to_ipv6_map.get(&dst_ipv4) {
        info!("TCP IPv4->IPv6 conversion: {}:{} -> {}:{} => IPv6 {}",
              src_ipv4, src_port, dst_ipv4, dst_port, mapped_ipv6);

        // Track the connection
        track_tcp_connection(&conn_key, tcp_header, tracker, mapped_ipv6, dst_ipv4.into(), is_outbound);

        // Convert packet to IPv6 - use WinTun virtual interface IPv6 address as source
        let wintun_ipv6 = "fe80::1234:5678:9abc:def0".parse::<Ipv6Addr>().unwrap(); // 虚拟网卡的IPv6地址
        let converted = convert_ipv4_to_ipv6_tcp(
            packet_data,
            headers,
            ipv4_header,
            tcp_header,
            wintun_ipv6,              // Source becomes WinTun IPv6 address
            mapped_ipv6.into()        // Destination becomes target IPv6
        );

        if let Some(ref packet) = converted {
            info!("TCP packet successfully converted to IPv6");
            debug!("Converted IPv6 packet length: {} bytes", packet.len());
            debug!("IPv6 packet header (first 40 bytes): {:?}", &packet[..std::cmp::min(40, packet.len())]);
        } else {
            error!("Failed to convert TCP packet to IPv6");
        }

        return converted;

    } else if let Some(&_mapped_ipv6) = ipv4_to_ipv6_map.get(&src_ipv4) {
        // Check if this is return traffic for an existing connection
        let reverse_key = ConnectionKey {
            src_ip: IpAddr::V4(dst_ipv4),
            dst_ip: IpAddr::V4(src_ipv4),
            src_port: dst_port,
            dst_port: src_port,
            protocol: ipv4_header.protocol,
        };

        if tracker.lock().unwrap().contains_key(&reverse_key) {
            info!("TCP return traffic: {}:{} -> {}:{}", src_ipv4, src_port, dst_ipv4, dst_port);
            // This is return traffic, forward as-is
            return Some(packet_data.to_vec());
        }
    }

    None
}

// Process UDP packets
fn process_udp_packet(
    packet_data: &[u8],
    headers: &etherparse::PacketHeaders,
    ipv4_header: &etherparse::Ipv4Header,
    udp_header: &etherparse::UdpHeader,
    src_ipv4: Ipv4Addr,
    dst_ipv4: Ipv4Addr,
    ipv4_to_ipv6_map: &HashMap<Ipv4Addr, Ipv6Addr>,
    is_outbound: bool,
) -> Option<Vec<u8>> {
    let src_port = udp_header.source_port;
    let dst_port = udp_header.destination_port;

    // Check if destination needs to be converted (IPv4 -> IPv6)
    if let Some(&mapped_ipv6) = ipv4_to_ipv6_map.get(&dst_ipv4) {
        info!("UDP IPv4->IPv6 conversion: {}:{} -> {}:{} => IPv6 {}",
              src_ipv4, src_port, dst_ipv4, dst_port, mapped_ipv6);
        return convert_ipv4_to_ipv6_udp(
            packet_data,
            headers,
            ipv4_header,
            udp_header,
            src_ipv4.to_ipv6_mapped(), // Source becomes IPv4-mapped IPv6
            mapped_ipv6.into()         // Destination becomes target IPv6
        );
    } else if let Some(&mapped_ipv6) = ipv4_to_ipv6_map.get(&src_ipv4) {
        info!("UDP return traffic: {}:{} -> {}:{}", src_ipv4, src_port, dst_ipv4, dst_port);
        // For UDP, we can forward return traffic directly
        return Some(packet_data.to_vec());
    }

    None
}

// Track TCP connection state
fn track_tcp_connection(
    conn_key: &ConnectionKey,
    tcp_header: &etherparse::TcpHeader,
    tracker: &ConnectionTracker,
    mapped_ipv6: Ipv6Addr,
    dst_ip: IpAddr,
    is_outbound: bool,
) {
    let mut connections = tracker.lock().unwrap();

    // Determine TCP state based on flags
    let new_state = if tcp_header.syn && !tcp_header.ack {
        TcpState::SynSent
    } else if tcp_header.syn && tcp_header.ack {
        TcpState::SynReceived
    } else if tcp_header.fin {
        TcpState::FinWait1
    } else if tcp_header.rst {
        TcpState::Closed
    } else {
        TcpState::Established
    };

    // Create or update connection entry
    let entry = ConnectionEntry {
        ipv4_src: if let IpAddr::V4(ip) = conn_key.src_ip { ip } else { return; },
        ipv4_dst: if let IpAddr::V4(ip) = conn_key.dst_ip { ip } else { return; },
        ipv4_src_port: conn_key.src_port,
        ipv4_dst_port: conn_key.dst_port,
        ipv6_src: mapped_ipv6,
        ipv6_dst: if let IpAddr::V6(ip) = dst_ip { ip } else {
            // Convert IPv4 to IPv4-mapped IPv6
            if let IpAddr::V4(ipv4) = dst_ip {
                ipv4.to_ipv6_mapped()
            } else {
                return;
            }
        },
        ipv6_src_port: conn_key.src_port,
        ipv6_dst_port: conn_key.dst_port,
        tcp_state: new_state.clone(),
        last_activity: Instant::now(),
        seq_offset: 0,
        ack_offset: 0,
    };

    connections.insert(conn_key.clone(), entry);
    debug!("Tracked TCP connection: {:?} -> {:?}", conn_key, new_state);
}

// Process IPv6 return traffic
fn process_ipv6_return_traffic(
    packet_data: &[u8],
    headers: &etherparse::PacketHeaders,
    ipv6_header: &etherparse::Ipv6Header,
    tracker: &ConnectionTracker,
    is_outbound: bool,
) -> Option<Vec<u8>> {
    let src_ipv6 = Ipv6Addr::from(ipv6_header.source);
    let dst_ipv6 = Ipv6Addr::from(ipv6_header.destination);

    info!("Processing IPv6 return traffic: {} -> {} (outbound: {})", src_ipv6, dst_ipv6, is_outbound);

    // Extract port information if available
    if let Some(ref transport) = headers.transport {
        match transport {
            TransportHeader::Tcp(tcp_header) => {
                info!("IPv6 TCP return traffic: {}:{} -> {}:{}",
                      src_ipv6, tcp_header.source_port, dst_ipv6, tcp_header.destination_port);

                // Look for matching connection
                let connections = tracker.lock().unwrap();
                for (_key, entry) in connections.iter() {
                    // Check if this is return traffic for our tracked connection
                    // Original: IPv4 src -> IPv6 dst (we sent with WinTun IPv6 as source)
                    // Return:   IPv6 dst -> WinTun IPv6 (fe80::1234:5678:9abc:def0)
                    if entry.ipv6_dst == src_ipv6 &&
                       dst_ipv6.to_string() == "fe80::1234:5678:9abc:def0" &&
                       entry.ipv6_src_port == tcp_header.destination_port &&
                       entry.ipv6_dst_port == tcp_header.source_port {

                        info!("Found matching connection for IPv6 return traffic");
                        info!("Converting IPv6 -> IPv4: {} -> {}", src_ipv6, entry.ipv4_src);

                        // Convert back to IPv4
                        return convert_ipv6_to_ipv4_tcp(
                            packet_data, headers, ipv6_header, tcp_header,
                            entry.ipv4_dst.into(), entry.ipv4_src.into() // dst->src, src->dst
                        );
                    }
                }

                // If no exact match, check if destination is IPv4-mapped
                if let Some(dst_ipv4) = dst_ipv6.to_ipv4_mapped() {
                    info!("Converting IPv6 return traffic to IPv4-mapped address: {} -> {}", src_ipv6, dst_ipv4);
                    return convert_ipv6_to_ipv4_tcp(
                        packet_data, headers, ipv6_header, tcp_header,
                        src_ipv6.into(), dst_ipv4.into()
                    );
                }
            }
            TransportHeader::Udp(udp_header) => {
                info!("IPv6 UDP return traffic: {}:{} -> {}:{}",
                      src_ipv6, udp_header.source_port, dst_ipv6, udp_header.destination_port);

                // For UDP, try to convert if destination is IPv4-mapped
                if let Some(dst_ipv4) = dst_ipv6.to_ipv4_mapped() {
                    return convert_ipv6_to_ipv4_udp_simple(
                        packet_data, ipv6_header, udp_header,
                        src_ipv6, dst_ipv4
                    );
                }
            }
            _ => {
                debug!("IPv6 non-TCP/UDP traffic: {} -> {}", src_ipv6, dst_ipv6);
            }
        }
    }

    None
}

// Convert IPv4 TCP packet to IPv6 (for raw IP packets without Ethernet header)
fn convert_ipv4_to_ipv6_tcp(
    packet_data: &[u8],
    headers: &etherparse::PacketHeaders,
    ipv4_header: &etherparse::Ipv4Header,
    tcp_header: &etherparse::TcpHeader,
    src_ipv6: Ipv6Addr,
    dst_ipv6: IpAddr,
) -> Option<Vec<u8>> {
    let dst_ipv6_addr = match dst_ipv6 {
        IpAddr::V6(addr) => addr,
        IpAddr::V4(ipv4) => ipv4.to_ipv6_mapped(),
    };

    // Calculate payload size (no Ethernet header in WinDivert packets)
    let ipv4_header_len = ipv4_header.header_len() as usize;
    let tcp_header_len = tcp_header.header_len() as usize;
    let payload_start = ipv4_header_len + tcp_header_len;
    let payload_len = packet_data.len() - payload_start;

    // Create new IPv6 packet (raw IP packet, no Ethernet header)
    let mut new_packet = Vec::new();

    // IPv6 header (40 bytes)
    let mut ipv6_header = [0u8; 40];
    ipv6_header[0] = 0x60; // Version 6
    // Traffic class and flow label remain 0

    // Payload length (TCP header + payload)
    let ipv6_payload_len = (tcp_header_len + payload_len) as u16;
    ipv6_header[4] = (ipv6_payload_len >> 8) as u8;
    ipv6_header[5] = (ipv6_payload_len & 0xFF) as u8;

    // Next header (TCP = 6)
    ipv6_header[6] = 6;

    // Hop limit
    ipv6_header[7] = ipv4_header.time_to_live;

    // Source address
    ipv6_header[8..24].copy_from_slice(&src_ipv6.octets());

    // Destination address
    ipv6_header[24..40].copy_from_slice(&dst_ipv6_addr.octets());

    new_packet.extend_from_slice(&ipv6_header);

    // TCP header with recalculated checksum
    let tcp_start = ipv4_header_len;
    let tcp_end = tcp_start + tcp_header_len;
    let mut tcp_bytes = packet_data[tcp_start..tcp_end].to_vec();

    // Zero out the checksum field
    tcp_bytes[16] = 0;
    tcp_bytes[17] = 0;

    // Calculate new TCP checksum for IPv6
    let tcp_checksum = calculate_tcp_checksum_ipv6(
        &src_ipv6.octets(),
        &dst_ipv6_addr.octets(),
        &tcp_bytes,
        &packet_data[payload_start..],
    );

    tcp_bytes[16] = (tcp_checksum >> 8) as u8;
    tcp_bytes[17] = (tcp_checksum & 0xFF) as u8;

    new_packet.extend_from_slice(&tcp_bytes);

    // Payload
    new_packet.extend_from_slice(&packet_data[payload_start..]);

    Some(new_packet)
}

// Convert IPv4 UDP packet to IPv6 (for raw IP packets without Ethernet header)
fn convert_ipv4_to_ipv6_udp(
    packet_data: &[u8],
    _headers: &etherparse::PacketHeaders,
    ipv4_header: &etherparse::Ipv4Header,
    _udp_header: &etherparse::UdpHeader,
    src_ipv6: Ipv6Addr,
    dst_ipv6: IpAddr,
) -> Option<Vec<u8>> {
    let dst_ipv6_addr = match dst_ipv6 {
        IpAddr::V6(addr) => addr,
        IpAddr::V4(ipv4) => ipv4.to_ipv6_mapped(),
    };

    // Calculate payload size (no Ethernet header in WinDivert packets)
    let ipv4_header_len = ipv4_header.header_len() as usize;
    let payload_start = ipv4_header_len + 8; // UDP header is 8 bytes
    let payload_len = packet_data.len() - payload_start;

    // Create new IPv6 packet (raw IP packet, no Ethernet header)
    let mut new_packet = Vec::new();

    // IPv6 header (40 bytes)
    let mut ipv6_header = [0u8; 40];
    ipv6_header[0] = 0x60; // Version 6

    // Payload length (UDP header + payload)
    let ipv6_payload_len = (8 + payload_len) as u16;
    ipv6_header[4] = (ipv6_payload_len >> 8) as u8;
    ipv6_header[5] = (ipv6_payload_len & 0xFF) as u8;

    // Next header (UDP = 17)
    ipv6_header[6] = 17;

    // Hop limit
    ipv6_header[7] = ipv4_header.time_to_live;

    // Source address
    ipv6_header[8..24].copy_from_slice(&src_ipv6.octets());

    // Destination address
    ipv6_header[24..40].copy_from_slice(&dst_ipv6_addr.octets());

    new_packet.extend_from_slice(&ipv6_header);

    // UDP header with recalculated checksum
    let udp_start = ipv4_header_len;
    let mut udp_bytes = packet_data[udp_start..udp_start + 8].to_vec();

    // Zero out the checksum field
    udp_bytes[6] = 0;
    udp_bytes[7] = 0;

    // Calculate new UDP checksum for IPv6
    let udp_checksum = calculate_udp_checksum_ipv6(
        &src_ipv6.octets(),
        &dst_ipv6_addr.octets(),
        &udp_bytes,
        &packet_data[payload_start..],
    );

    udp_bytes[6] = (udp_checksum >> 8) as u8;
    udp_bytes[7] = (udp_checksum & 0xFF) as u8;

    new_packet.extend_from_slice(&udp_bytes);

    // Payload
    new_packet.extend_from_slice(&packet_data[payload_start..]);

    Some(new_packet)
}

// Simple IPv6 to IPv4 UDP conversion for return traffic (raw IP packets)
fn convert_ipv6_to_ipv4_udp_simple(
    packet_data: &[u8],
    ipv6_header: &etherparse::Ipv6Header,
    _udp_header: &etherparse::UdpHeader,
    src_ipv6: Ipv6Addr,
    dst_ipv4: Ipv4Addr,
) -> Option<Vec<u8>> {
    // Try to extract IPv4 from IPv6 source, or use a default mapping
    let src_ipv4 = if let Some(ipv4) = src_ipv6.to_ipv4_mapped() {
        ipv4
    } else {
        // For link-local or other IPv6 addresses, map to the target address
        Ipv4Addr::new(192, 168, 200, 2)
    };

    // Calculate payload size (no Ethernet header in WinDivert packets)
    let payload_start = 40 + 8; // IPv6 header (40) + UDP header (8)
    let payload_len = packet_data.len() - payload_start;

    // Create new IPv4 packet (raw IP packet, no Ethernet header)
    let mut new_packet = Vec::new();

    // IPv4 header (20 bytes)
    let total_len = (20 + 8 + payload_len) as u16;
    let mut ipv4_header_bytes = [0u8; 20];
    ipv4_header_bytes[0] = 0x45; // Version 4, IHL 5
    ipv4_header_bytes[1] = 0x00; // DSCP + ECN
    ipv4_header_bytes[2] = (total_len >> 8) as u8;
    ipv4_header_bytes[3] = (total_len & 0xFF) as u8;
    ipv4_header_bytes[4] = 0x00; // Identification
    ipv4_header_bytes[5] = 0x00;
    ipv4_header_bytes[6] = 0x40; // Flags (Don't fragment)
    ipv4_header_bytes[7] = 0x00; // Fragment offset
    ipv4_header_bytes[8] = ipv6_header.hop_limit; // TTL
    ipv4_header_bytes[9] = 17; // Protocol (UDP)
    // Checksum will be calculated later
    ipv4_header_bytes[12..16].copy_from_slice(&src_ipv4.octets());
    ipv4_header_bytes[16..20].copy_from_slice(&dst_ipv4.octets());

    // Calculate IPv4 header checksum
    let header_checksum = calculate_ipv4_checksum(&ipv4_header_bytes);
    ipv4_header_bytes[10] = (header_checksum >> 8) as u8;
    ipv4_header_bytes[11] = (header_checksum & 0xFF) as u8;

    new_packet.extend_from_slice(&ipv4_header_bytes);

    // UDP header with recalculated checksum
    let udp_start = 40; // IPv6 header size
    let mut udp_bytes = packet_data[udp_start..udp_start + 8].to_vec();

    // Zero out the checksum field
    udp_bytes[6] = 0;
    udp_bytes[7] = 0;

    // Calculate new UDP checksum for IPv4
    let udp_checksum = calculate_udp_checksum_ipv4(
        &src_ipv4.octets(),
        &dst_ipv4.octets(),
        &udp_bytes,
        &packet_data[payload_start..],
    );

    udp_bytes[6] = (udp_checksum >> 8) as u8;
    udp_bytes[7] = (udp_checksum & 0xFF) as u8;

    new_packet.extend_from_slice(&udp_bytes);

    // Payload
    new_packet.extend_from_slice(&packet_data[payload_start..]);

    Some(new_packet)
}

// Legacy function - keeping for compatibility but should be replaced
fn convert_ipv4_to_ipv6(packet_data: &[u8], headers: &etherparse::PacketHeaders, src_ipv6: Ipv6Addr, _dst_ipv4: std::net::IpAddr) -> Option<Vec<u8>> {
    info!("Converting IPv4 packet to IPv6");
    debug!("Source IPv6: {}, Destination IPv4: {}", src_ipv6, _dst_ipv4);
    
    // Check if we have an IPv4 header
    if let Some(etherparse::IpHeader::Version4(ref ipv4_header, _)) = headers.ip {
        // Create a new packet with IPv6 header
        let mut new_packet = Vec::new();
        
        // Ethernet header (14 bytes) - keep the same for now
        new_packet.extend_from_slice(&packet_data[..14]);
        
        // IPv6 header (40 bytes)
        let mut ipv6_bytes = [0u8; 40];
        ipv6_bytes[0] = 0x60; // Version (4 bits) + Traffic Class (4 bits)
        ipv6_bytes[1] = 0x00; // Traffic Class (4 bits) + Flow Label (4 bits)
        ipv6_bytes[2] = 0x00; // Flow Label (8 bits)
        ipv6_bytes[3] = 0x00; // Flow Label (8 bits)
        
        // Payload length (2 bytes) - set later
        // Next header (1 byte) - same as IPv4 protocol
        ipv6_bytes[6] = ipv4_header.protocol;
        // Hop limit (1 byte) - same as IPv4 TTL
        ipv6_bytes[7] = ipv4_header.time_to_live;
        
        // Source address (16 bytes)
        let src_bytes = src_ipv6.octets();
        ipv6_bytes[8..24].copy_from_slice(&src_bytes);
        
        // Destination address (16 bytes) - for now, we'll use IPv4-mapped IPv6 address
        // This is a simplified approach - in a real implementation, you'd map to the actual IPv6 address
        let dst_ipv4 = std::net::Ipv4Addr::from(ipv4_header.destination);
        let dst_bytes = dst_ipv4.octets();
        ipv6_bytes[24] = 0x00;
        ipv6_bytes[25] = 0x00;
        ipv6_bytes[26] = 0x00;
        ipv6_bytes[27] = 0x00;
        ipv6_bytes[28] = 0x00;
        ipv6_bytes[29] = 0x00;
        ipv6_bytes[30] = 0x00;
        ipv6_bytes[31] = 0x00;
        ipv6_bytes[32] = 0x00;
        ipv6_bytes[33] = 0x00;
        ipv6_bytes[34] = 0xff;
        ipv6_bytes[35] = 0xff;
        ipv6_bytes[36] = dst_bytes[0];
        ipv6_bytes[37] = dst_bytes[1];
        ipv6_bytes[38] = dst_bytes[2];
        ipv6_bytes[39] = dst_bytes[3];
        
        new_packet.extend_from_slice(&ipv6_bytes);
        
        // Payload (everything after IPv4 header)
        let ipv4_header_len = ipv4_header.header_len() as usize;
        new_packet.extend_from_slice(&packet_data[14 + ipv4_header_len..]);
        
        // Update payload length in IPv6 header
        let payload_length = (new_packet.len() - 14 - 40) as u16;
        new_packet[14 + 4] = (payload_length >> 8) as u8;
        new_packet[14 + 5] = (payload_length & 0xFF) as u8;
        
        // Update EtherType to IPv6 (0x86DD)
        new_packet[12] = 0x86;
        new_packet[13] = 0xDD;
        
        Some(new_packet)
    } else {
        None
    }
}

// Function to convert IPv6 packet to IPv4
fn convert_ipv6_to_ipv4(packet_data: &[u8], headers: &etherparse::PacketHeaders, src_ipv4: std::net::IpAddr, dst_ipv6: Ipv6Addr) -> Option<Vec<u8>> {
    info!("Converting IPv6 packet to IPv4");
    debug!("Source IPv4: {}, Destination IPv6: {}", src_ipv4, dst_ipv6);
    
    // Check if we have an IPv6 header
    if let Some(etherparse::IpHeader::Version6(ref _ipv6_header, _)) = headers.ip {
        // Create a new packet with IPv4 header
        let mut new_packet = Vec::new();
        
        // Ethernet header (14 bytes) - keep the same for now
        new_packet.extend_from_slice(&packet_data[..14]);
        
        // IPv4 header (20 bytes minimum)
        let mut ipv4_bytes = [0u8; 20];
        ipv4_bytes[0] = 0x45; // Version (4 bits) + IHL (4 bits)
        ipv4_bytes[1] = 0x00; // DSCP (6 bits) + ECN (2 bits)
        
        // Total length (2 bytes) - set later
        // Identification (2 bytes) - keep the same
        ipv4_bytes[4] = 0x00;
        ipv4_bytes[5] = 0x00;
        
        // Flags (3 bits) + Fragment Offset (13 bits)
        ipv4_bytes[6] = 0x40; // Don't fragment flag
        ipv4_bytes[7] = 0x00; // Fragment offset
        
        // TTL (1 byte) - same as IPv6 hop limit
        // ipv4_bytes[8] = ipv6_header.hop_limit;
        ipv4_bytes[8] = 64; // Default value
        
        // Protocol (1 byte) - same as IPv6 next header
        // ipv4_bytes[9] = ipv6_header.next_header;
        ipv4_bytes[9] = 0x11; // UDP as default, should be set properly
        
        // Header checksum (2 bytes) - set later
        ipv4_bytes[10] = 0x00;
        ipv4_bytes[11] = 0x00;
        
        // Source address (4 bytes)
        if let std::net::IpAddr::V4(src_v4) = src_ipv4 {
            let src_bytes = src_v4.octets();
            ipv4_bytes[12] = src_bytes[0];
            ipv4_bytes[13] = src_bytes[1];
            ipv4_bytes[14] = src_bytes[2];
            ipv4_bytes[15] = src_bytes[3];
        }
        
        // Destination address (4 bytes) - extract from IPv6 address
        // This is a simplified approach - in a real implementation, you'd map from the actual IPv6 address
        let dst_bytes = dst_ipv6.octets();
        // Check if it's an IPv4-mapped IPv6 address
        if dst_bytes[0] == 0x00 && dst_bytes[1] == 0x00 && dst_bytes[2] == 0x00 && dst_bytes[3] == 0x00 &&
           dst_bytes[4] == 0x00 && dst_bytes[5] == 0x00 && dst_bytes[6] == 0x00 && dst_bytes[7] == 0x00 &&
           dst_bytes[8] == 0x00 && dst_bytes[9] == 0x00 && dst_bytes[10] == 0xFF && dst_bytes[11] == 0xFF {
            ipv4_bytes[16] = dst_bytes[12];
            ipv4_bytes[17] = dst_bytes[13];
            ipv4_bytes[18] = dst_bytes[14];
            ipv4_bytes[19] = dst_bytes[15];
        } else {
            // For other IPv6 addresses, we'll use a default mapping
            ipv4_bytes[16] = 192;
            ipv4_bytes[17] = 168;
            ipv4_bytes[18] = 200;
            ipv4_bytes[19] = 3;
        }
        
        new_packet.extend_from_slice(&ipv4_bytes);
        
        // Payload (everything after IPv6 header)
        new_packet.extend_from_slice(&packet_data[14 + 40..]);
        
        // Update total length in IPv4 header
        let total_length = (new_packet.len() - 14) as u16;
        new_packet[14 + 2] = (total_length >> 8) as u8;
        new_packet[14 + 3] = (total_length & 0xFF) as u8;
        
        // Calculate and update header checksum
        let checksum = calculate_ipv4_checksum(&ipv4_bytes);
        new_packet[14 + 10] = (checksum >> 8) as u8;
        new_packet[14 + 11] = (checksum & 0xFF) as u8;
        
        // Update EtherType to IPv4 (0x0800)
        new_packet[12] = 0x08;
        new_packet[13] = 0x00;
        
        Some(new_packet)
    } else {
        None
    }
}

// Helper function to calculate IPv4 header checksum
fn calculate_ipv4_checksum(header: &[u8]) -> u16 {
    let mut sum = 0u32;

    // Sum all 16-bit words
    for i in 0..header.len() / 2 {
        let word = ((header[i * 2] as u32) << 8) | (header[i * 2 + 1] as u32);
        sum += word;
    }

    // Handle odd byte
    if header.len() % 2 == 1 {
        sum += (header[header.len() - 1] as u32) << 8;
    }

    // Add carry bits
    while (sum >> 16) != 0 {
        sum = (sum & 0xFFFF) + (sum >> 16);
    }

    // One's complement
    !sum as u16
}

// Calculate TCP checksum for IPv6
fn calculate_tcp_checksum_ipv6(src_ip: &[u8; 16], dst_ip: &[u8; 16], tcp_header: &[u8], payload: &[u8]) -> u16 {
    let mut sum = 0u32;

    // IPv6 pseudo-header
    // Source address (16 bytes)
    for i in 0..8 {
        let word = ((src_ip[i * 2] as u32) << 8) | (src_ip[i * 2 + 1] as u32);
        sum += word;
    }

    // Destination address (16 bytes)
    for i in 0..8 {
        let word = ((dst_ip[i * 2] as u32) << 8) | (dst_ip[i * 2 + 1] as u32);
        sum += word;
    }

    // TCP length
    let tcp_length = (tcp_header.len() + payload.len()) as u32;
    sum += tcp_length;

    // Next header (TCP = 6)
    sum += 6;

    // TCP header
    for i in 0..tcp_header.len() / 2 {
        let word = ((tcp_header[i * 2] as u32) << 8) | (tcp_header[i * 2 + 1] as u32);
        sum += word;
    }

    // Handle odd byte in TCP header
    if tcp_header.len() % 2 == 1 {
        sum += (tcp_header[tcp_header.len() - 1] as u32) << 8;
    }

    // Payload
    for i in 0..payload.len() / 2 {
        let word = ((payload[i * 2] as u32) << 8) | (payload[i * 2 + 1] as u32);
        sum += word;
    }

    // Handle odd byte in payload
    if payload.len() % 2 == 1 {
        sum += (payload[payload.len() - 1] as u32) << 8;
    }

    // Add carry bits
    while (sum >> 16) != 0 {
        sum = (sum & 0xFFFF) + (sum >> 16);
    }

    // One's complement
    !sum as u16
}

// Calculate TCP checksum for IPv4
fn calculate_tcp_checksum_ipv4(src_ip: &[u8; 4], dst_ip: &[u8; 4], tcp_header: &[u8], payload: &[u8]) -> u16 {
    let mut sum = 0u32;

    // IPv4 pseudo-header
    // Source address (4 bytes)
    sum += ((src_ip[0] as u32) << 8) | (src_ip[1] as u32);
    sum += ((src_ip[2] as u32) << 8) | (src_ip[3] as u32);

    // Destination address (4 bytes)
    sum += ((dst_ip[0] as u32) << 8) | (dst_ip[1] as u32);
    sum += ((dst_ip[2] as u32) << 8) | (dst_ip[3] as u32);

    // Protocol (TCP = 6)
    sum += 6;

    // TCP length
    let tcp_length = (tcp_header.len() + payload.len()) as u32;
    sum += tcp_length;

    // TCP header
    for i in 0..tcp_header.len() / 2 {
        let word = ((tcp_header[i * 2] as u32) << 8) | (tcp_header[i * 2 + 1] as u32);
        sum += word;
    }

    // Handle odd byte in TCP header
    if tcp_header.len() % 2 == 1 {
        sum += (tcp_header[tcp_header.len() - 1] as u32) << 8;
    }

    // Payload
    for i in 0..payload.len() / 2 {
        let word = ((payload[i * 2] as u32) << 8) | (payload[i * 2 + 1] as u32);
        sum += word;
    }

    // Handle odd byte in payload
    if payload.len() % 2 == 1 {
        sum += (payload[payload.len() - 1] as u32) << 8;
    }

    // Add carry bits
    while (sum >> 16) != 0 {
        sum = (sum & 0xFFFF) + (sum >> 16);
    }

    // One's complement
    !sum as u16
}

// Calculate UDP checksum for IPv6
fn calculate_udp_checksum_ipv6(src_ip: &[u8; 16], dst_ip: &[u8; 16], udp_header: &[u8], payload: &[u8]) -> u16 {
    let mut sum = 0u32;

    // IPv6 pseudo-header
    // Source address (16 bytes)
    for i in 0..8 {
        let word = ((src_ip[i * 2] as u32) << 8) | (src_ip[i * 2 + 1] as u32);
        sum += word;
    }

    // Destination address (16 bytes)
    for i in 0..8 {
        let word = ((dst_ip[i * 2] as u32) << 8) | (dst_ip[i * 2 + 1] as u32);
        sum += word;
    }

    // UDP length
    let udp_length = (udp_header.len() + payload.len()) as u32;
    sum += udp_length;

    // Next header (UDP = 17)
    sum += 17;

    // UDP header
    for i in 0..udp_header.len() / 2 {
        let word = ((udp_header[i * 2] as u32) << 8) | (udp_header[i * 2 + 1] as u32);
        sum += word;
    }

    // Payload
    for i in 0..payload.len() / 2 {
        let word = ((payload[i * 2] as u32) << 8) | (payload[i * 2 + 1] as u32);
        sum += word;
    }

    // Handle odd byte in payload
    if payload.len() % 2 == 1 {
        sum += (payload[payload.len() - 1] as u32) << 8;
    }

    // Add carry bits
    while (sum >> 16) != 0 {
        sum = (sum & 0xFFFF) + (sum >> 16);
    }

    // One's complement
    !sum as u16
}

// Calculate UDP checksum for IPv4
fn calculate_udp_checksum_ipv4(src_ip: &[u8; 4], dst_ip: &[u8; 4], udp_header: &[u8], payload: &[u8]) -> u16 {
    let mut sum = 0u32;

    // IPv4 pseudo-header
    // Source address (4 bytes)
    sum += ((src_ip[0] as u32) << 8) | (src_ip[1] as u32);
    sum += ((src_ip[2] as u32) << 8) | (src_ip[3] as u32);

    // Destination address (4 bytes)
    sum += ((dst_ip[0] as u32) << 8) | (dst_ip[1] as u32);
    sum += ((dst_ip[2] as u32) << 8) | (dst_ip[3] as u32);

    // Protocol (UDP = 17)
    sum += 17;

    // UDP length
    let udp_length = (udp_header.len() + payload.len()) as u32;
    sum += udp_length;

    // UDP header
    for i in 0..udp_header.len() / 2 {
        let word = ((udp_header[i * 2] as u32) << 8) | (udp_header[i * 2 + 1] as u32);
        sum += word;
    }

    // Payload
    for i in 0..payload.len() / 2 {
        let word = ((payload[i * 2] as u32) << 8) | (payload[i * 2 + 1] as u32);
        sum += word;
    }

    // Handle odd byte in payload
    if payload.len() % 2 == 1 {
        sum += (payload[payload.len() - 1] as u32) << 8;
    }

    // Add carry bits
    while (sum >> 16) != 0 {
        sum = (sum & 0xFFFF) + (sum >> 16);
    }

    // One's complement
    !sum as u16
}

// Convert IPv6 TCP packet to IPv4
fn convert_ipv6_to_ipv4_tcp(
    packet_data: &[u8],
    headers: &etherparse::PacketHeaders,
    ipv6_header: &etherparse::Ipv6Header,
    tcp_header: &etherparse::TcpHeader,
    src_ipv4: IpAddr,
    dst_ipv4: IpAddr,
) -> Option<Vec<u8>> {
    let src_ipv4_addr = match src_ipv4 {
        IpAddr::V4(addr) => addr,
        IpAddr::V6(ipv6) => ipv6.to_ipv4_mapped()?,
    };

    let dst_ipv4_addr = match dst_ipv4 {
        IpAddr::V4(addr) => addr,
        IpAddr::V6(ipv6) => ipv6.to_ipv4_mapped()?,
    };

    // Calculate payload size (no Ethernet header in WinDivert packets)
    let tcp_header_len = tcp_header.header_len() as usize;
    let payload_start = 40 + tcp_header_len; // IPv6 header is 40 bytes
    let payload_len = packet_data.len() - payload_start;

    // Create new IPv4 packet (raw IP packet, no Ethernet header)
    let mut new_packet = Vec::new();

    // IPv4 header (20 bytes)
    let total_len = (20 + tcp_header_len + payload_len) as u16;
    let mut ipv4_header_bytes = [0u8; 20];
    ipv4_header_bytes[0] = 0x45; // Version 4, IHL 5
    ipv4_header_bytes[1] = 0x00; // DSCP + ECN
    ipv4_header_bytes[2] = (total_len >> 8) as u8;
    ipv4_header_bytes[3] = (total_len & 0xFF) as u8;
    ipv4_header_bytes[4] = 0x00; // Identification
    ipv4_header_bytes[5] = 0x00;
    ipv4_header_bytes[6] = 0x40; // Flags (Don't fragment)
    ipv4_header_bytes[7] = 0x00; // Fragment offset
    ipv4_header_bytes[8] = ipv6_header.hop_limit; // TTL
    ipv4_header_bytes[9] = 6; // Protocol (TCP)
    // Checksum will be calculated later
    ipv4_header_bytes[12..16].copy_from_slice(&src_ipv4_addr.octets());
    ipv4_header_bytes[16..20].copy_from_slice(&dst_ipv4_addr.octets());

    // Calculate IPv4 header checksum
    let header_checksum = calculate_ipv4_checksum(&ipv4_header_bytes);
    ipv4_header_bytes[10] = (header_checksum >> 8) as u8;
    ipv4_header_bytes[11] = (header_checksum & 0xFF) as u8;

    new_packet.extend_from_slice(&ipv4_header_bytes);

    // TCP header with recalculated checksum
    let tcp_start = 40; // IPv6 header size
    let mut tcp_bytes = packet_data[tcp_start..tcp_start + tcp_header_len].to_vec();

    // Zero out the checksum field
    tcp_bytes[16] = 0;
    tcp_bytes[17] = 0;

    // Calculate new TCP checksum for IPv4
    let tcp_checksum = calculate_tcp_checksum_ipv4(
        &src_ipv4_addr.octets(),
        &dst_ipv4_addr.octets(),
        &tcp_bytes,
        &packet_data[payload_start..],
    );

    tcp_bytes[16] = (tcp_checksum >> 8) as u8;
    tcp_bytes[17] = (tcp_checksum & 0xFF) as u8;

    new_packet.extend_from_slice(&tcp_bytes);

    // Payload
    new_packet.extend_from_slice(&packet_data[payload_start..]);

    Some(new_packet)
}

// Include tests module
#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{Ipv4Addr, Ipv6Addr};
    use std::collections::HashMap;
    use std::sync::{Arc, Mutex};

    // Helper function to create a test IPv4 packet
    fn create_test_ipv4_tcp_packet(
        src_ip: Ipv4Addr,
        dst_ip: Ipv4Addr,
        src_port: u16,
        dst_port: u16,
        syn: bool,
        ack: bool,
    ) -> Vec<u8> {
        let mut packet = Vec::new();

        // Ethernet header (14 bytes)
        packet.extend_from_slice(&[0x00; 12]); // MAC addresses
        packet.push(0x08); // IPv4 EtherType
        packet.push(0x00);

        // IPv4 header (20 bytes)
        packet.push(0x45); // Version 4, IHL 5
        packet.push(0x00); // DSCP + ECN
        packet.push(0x00); // Total length (will be set)
        packet.push(0x28); // 40 bytes total (20 IP + 20 TCP)
        packet.push(0x00); // Identification
        packet.push(0x00);
        packet.push(0x40); // Flags (Don't fragment)
        packet.push(0x00); // Fragment offset
        packet.push(0x40); // TTL
        packet.push(0x06); // Protocol (TCP)
        packet.push(0x00); // Checksum (will be calculated)
        packet.push(0x00);
        packet.extend_from_slice(&src_ip.octets());
        packet.extend_from_slice(&dst_ip.octets());

        // TCP header (20 bytes)
        packet.push((src_port >> 8) as u8);
        packet.push((src_port & 0xFF) as u8);
        packet.push((dst_port >> 8) as u8);
        packet.push((dst_port & 0xFF) as u8);
        packet.extend_from_slice(&[0x00; 4]); // Sequence number
        packet.extend_from_slice(&[0x00; 4]); // Acknowledgment number
        packet.push(0x50); // Data offset (5 * 4 = 20 bytes)

        // TCP flags
        let mut flags = 0u8;
        if syn { flags |= 0x02; }
        if ack { flags |= 0x10; }
        packet.push(flags);

        packet.push(0x20); // Window size
        packet.push(0x00);
        packet.push(0x00); // Checksum (will be calculated)
        packet.push(0x00);
        packet.push(0x00); // Urgent pointer
        packet.push(0x00);

        packet
    }

    #[test]
    fn test_traffic_filtering() {
        let filters = TrafficFilters {
            ipv4_subnets: vec!["*************/24".to_string(), "10.0.0.0/8".to_string()],
            ipv6_subnets: vec!["2001:db8::/32".to_string()],
        };

        let (ipv4_networks, ipv6_networks) = parse_traffic_filters(&filters);

        // Test IPv4 filtering
        assert!(should_process_ipv4(Ipv4Addr::new(192, 168, 200, 1), &ipv4_networks));
        assert!(should_process_ipv4(Ipv4Addr::new(10, 0, 0, 1), &ipv4_networks));
        assert!(!should_process_ipv4(Ipv4Addr::new(172, 16, 0, 1), &ipv4_networks));

        // Test IPv6 filtering
        assert!(should_process_ipv6(Ipv6Addr::new(0x2001, 0xdb8, 0, 0, 0, 0, 0, 1), &ipv6_networks));
        assert!(!should_process_ipv6(Ipv6Addr::new(0xfe80, 0, 0, 0, 0, 0, 0, 1), &ipv6_networks));
    }

    #[test]
    fn test_checksum_calculation() {
        // Test IPv4 header checksum
        let header = [
            0x45, 0x00, 0x00, 0x3c, 0x1c, 0x46, 0x40, 0x00,
            0x40, 0x06, 0x00, 0x00, 0xac, 0x10, 0x0a, 0x63,
            0xac, 0x10, 0x0a, 0x0c
        ];

        let checksum = calculate_ipv4_checksum(&header);
        // The checksum should be calculated correctly
        assert_ne!(checksum, 0);
    }
}