@echo off
echo ========================================
echo HTTP Test Script
echo ========================================
echo.
echo Testing HTTP connectivity to verify the proxy works correctly.
echo.
echo 1. Testing direct IPv6 access (should work):
echo    curl -6 "http://[fe80::ca75:f4ff:fe67:c094]/"
echo.
curl -6 -m 10 "http://[fe80::ca75:f4ff:fe67:c094]/"
echo.
echo ========================================
echo.
echo 2. Now start TProxy and test IPv4 to IPv6 conversion:
echo    curl "http://*************/"
echo.
echo Expected log messages:
echo - "TCP IPv4->IPv6 conversion: *************:XXXX -> *************:80 => IPv6 fe80::ca75:f4ff:fe67:c094"
echo - "Processing IPv6 return traffic: fe80::ca75:f4ff:fe67:c094 -> [IPv4-mapped]"
echo.
echo Press any key to continue...
pause > nul
