#[cfg(test)]
mod tests {
    use super::*;
    use std::net::{Ipv4Addr, Ipv6Addr};
    use std::collections::HashMap;
    use std::sync::{Arc, Mutex};

    // Helper function to create a test IPv4 packet
    fn create_test_ipv4_tcp_packet(
        src_ip: Ipv4Addr,
        dst_ip: Ipv4Addr,
        src_port: u16,
        dst_port: u16,
        syn: bool,
        ack: bool,
    ) -> Vec<u8> {
        let mut packet = Vec::new();
        
        // Ethernet header (14 bytes)
        packet.extend_from_slice(&[0x00; 12]); // MAC addresses
        packet.push(0x08); // IPv4 EtherType
        packet.push(0x00);
        
        // IPv4 header (20 bytes)
        packet.push(0x45); // Version 4, IHL 5
        packet.push(0x00); // DSCP + ECN
        packet.push(0x00); // Total length (will be set)
        packet.push(0x28); // 40 bytes total (20 IP + 20 TCP)
        packet.push(0x00); // Identification
        packet.push(0x00);
        packet.push(0x40); // Flags (Don't fragment)
        packet.push(0x00); // Fragment offset
        packet.push(0x40); // TTL
        packet.push(0x06); // Protocol (TCP)
        packet.push(0x00); // Checksum (will be calculated)
        packet.push(0x00);
        packet.extend_from_slice(&src_ip.octets());
        packet.extend_from_slice(&dst_ip.octets());
        
        // TCP header (20 bytes)
        packet.push((src_port >> 8) as u8);
        packet.push((src_port & 0xFF) as u8);
        packet.push((dst_port >> 8) as u8);
        packet.push((dst_port & 0xFF) as u8);
        packet.extend_from_slice(&[0x00; 4]); // Sequence number
        packet.extend_from_slice(&[0x00; 4]); // Acknowledgment number
        packet.push(0x50); // Data offset (5 * 4 = 20 bytes)
        
        // TCP flags
        let mut flags = 0u8;
        if syn { flags |= 0x02; }
        if ack { flags |= 0x10; }
        packet.push(flags);
        
        packet.push(0x20); // Window size
        packet.push(0x00);
        packet.push(0x00); // Checksum (will be calculated)
        packet.push(0x00);
        packet.push(0x00); // Urgent pointer
        packet.push(0x00);
        
        packet
    }

    #[test]
    fn test_tcp_connection_tracking() {
        let tracker: ConnectionTracker = Arc::new(Mutex::new(HashMap::new()));
        
        // Create a test TCP SYN packet
        let packet = create_test_ipv4_tcp_packet(
            Ipv4Addr::new(192, 168, 200, 2),
            Ipv4Addr::new(192, 168, 1, 100),
            12345,
            80,
            true,  // SYN
            false, // ACK
        );
        
        // Parse the packet
        let headers = etherparse::PacketHeaders::from_ethernet_slice(&packet).unwrap();
        
        if let Some(IpHeader::Version4(ipv4_header, _)) = &headers.ip {
            if let Some(TransportHeader::Tcp(tcp_header)) = &headers.transport {
                let conn_key = ConnectionKey {
                    src_ip: IpAddr::V4(Ipv4Addr::from(ipv4_header.source)),
                    dst_ip: IpAddr::V4(Ipv4Addr::from(ipv4_header.destination)),
                    src_port: tcp_header.source_port,
                    dst_port: tcp_header.destination_port,
                    protocol: ipv4_header.protocol,
                };
                
                // Track the connection
                track_tcp_connection(
                    &conn_key,
                    tcp_header,
                    &tracker,
                    Ipv6Addr::new(0x2001, 0xdb8, 0, 0, 0, 0, 0, 1),
                    IpAddr::V4(Ipv4Addr::new(192, 168, 1, 100)),
                    true,
                );
                
                // Verify the connection was tracked
                let connections = tracker.lock().unwrap();
                assert!(connections.contains_key(&conn_key));
                
                let entry = connections.get(&conn_key).unwrap();
                assert_eq!(entry.tcp_state, TcpState::SynSent);
                assert_eq!(entry.ipv4_src, Ipv4Addr::new(192, 168, 200, 2));
                assert_eq!(entry.ipv4_dst, Ipv4Addr::new(192, 168, 1, 100));
            }
        }
    }

    #[test]
    fn test_ipv4_to_ipv6_tcp_conversion() {
        let packet = create_test_ipv4_tcp_packet(
            Ipv4Addr::new(192, 168, 200, 2),
            Ipv4Addr::new(192, 168, 1, 100),
            12345,
            80,
            true,
            false,
        );
        
        let headers = etherparse::PacketHeaders::from_ethernet_slice(&packet).unwrap();
        
        if let Some(IpHeader::Version4(ipv4_header, _)) = &headers.ip {
            if let Some(TransportHeader::Tcp(tcp_header)) = &headers.transport {
                let converted = convert_ipv4_to_ipv6_tcp(
                    &packet,
                    &headers,
                    ipv4_header,
                    tcp_header,
                    Ipv6Addr::new(0x2001, 0xdb8, 0, 0, 0, 0, 0, 1),
                    IpAddr::V4(Ipv4Addr::new(192, 168, 1, 100)),
                );
                
                assert!(converted.is_some());
                let converted_packet = converted.unwrap();
                
                // Verify EtherType is IPv6
                assert_eq!(converted_packet[12], 0x86);
                assert_eq!(converted_packet[13], 0xDD);
                
                // Verify IPv6 version
                assert_eq!(converted_packet[14] >> 4, 6);
            }
        }
    }

    #[test]
    fn test_traffic_filtering() {
        let filters = TrafficFilters {
            ipv4_subnets: vec!["*************/24".to_string(), "10.0.0.0/8".to_string()],
            ipv6_subnets: vec!["2001:db8::/32".to_string()],
        };
        
        let (ipv4_networks, ipv6_networks) = parse_traffic_filters(&filters);
        
        // Test IPv4 filtering
        assert!(should_process_ipv4(Ipv4Addr::new(192, 168, 200, 1), &ipv4_networks));
        assert!(should_process_ipv4(Ipv4Addr::new(10, 0, 0, 1), &ipv4_networks));
        assert!(!should_process_ipv4(Ipv4Addr::new(172, 16, 0, 1), &ipv4_networks));
        
        // Test IPv6 filtering
        assert!(should_process_ipv6(Ipv6Addr::new(0x2001, 0xdb8, 0, 0, 0, 0, 0, 1), &ipv6_networks));
        assert!(!should_process_ipv6(Ipv6Addr::new(0xfe80, 0, 0, 0, 0, 0, 0, 1), &ipv6_networks));
    }

    #[test]
    fn test_config_loading() {
        // This test requires the config.yaml file to be present
        // In a real test environment, you might want to create a test config
        let config = load_config();
        
        assert!(!config.address_mappings.is_empty());
        assert!(!config.traffic_filters.ipv4_subnets.is_empty());
        assert_eq!(config.interfaces.wintun.name, "TProxy-Wintun");
    }

    #[test]
    fn test_checksum_calculation() {
        // Test IPv4 header checksum
        let header = [
            0x45, 0x00, 0x00, 0x3c, 0x1c, 0x46, 0x40, 0x00,
            0x40, 0x06, 0x00, 0x00, 0xac, 0x10, 0x0a, 0x63,
            0xac, 0x10, 0x0a, 0x0c
        ];
        
        let checksum = calculate_ipv4_checksum(&header);
        // The checksum should be calculated correctly
        assert_ne!(checksum, 0);
    }

    #[test]
    fn test_connection_cleanup() {
        let tracker: ConnectionTracker = Arc::new(Mutex::new(HashMap::new()));
        
        // Add a test connection
        let conn_key = ConnectionKey {
            src_ip: IpAddr::V4(Ipv4Addr::new(192, 168, 200, 2)),
            dst_ip: IpAddr::V4(Ipv4Addr::new(192, 168, 1, 100)),
            src_port: 12345,
            dst_port: 80,
            protocol: 6,
        };
        
        let entry = ConnectionEntry {
            ipv4_src: Ipv4Addr::new(192, 168, 200, 2),
            ipv4_dst: Ipv4Addr::new(192, 168, 1, 100),
            ipv4_src_port: 12345,
            ipv4_dst_port: 80,
            ipv6_src: Ipv6Addr::new(0x2001, 0xdb8, 0, 0, 0, 0, 0, 1),
            ipv6_dst: Ipv6Addr::new(0x2001, 0xdb8, 0, 0, 0, 0, 0, 100),
            ipv6_src_port: 12345,
            ipv6_dst_port: 80,
            tcp_state: TcpState::Established,
            last_activity: Instant::now() - Duration::from_secs(400), // Expired
            seq_offset: 0,
            ack_offset: 0,
        };
        
        tracker.lock().unwrap().insert(conn_key.clone(), entry);
        
        // Cleanup should remove the expired connection
        cleanup_expired_connections(&tracker);
        
        assert!(!tracker.lock().unwrap().contains_key(&conn_key));
    }
}
