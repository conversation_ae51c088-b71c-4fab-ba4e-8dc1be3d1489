//go:build windows

package wintun

import (
	"fmt"
	"net"
	"runtime"
	"sync"
	"time"

	"golang.zx2c4.com/wintun"

	"tailscale-nat46/pkg/logger"
)

// Interface represents a Wintun virtual network interface
type Interface struct {
	adapter *wintun.Adapter
	session wintun.Session
	logger  *logger.Logger

	// Configuration
	name    string
	localIP net.IP
	mtu     int

	// State
	running bool
	mu      sync.RWMutex

	// Channels for packet I/O
	inbound  chan []byte
	outbound chan []byte

	// Control
	stopCh chan struct{}
	wg     sync.WaitGroup
}

// Config represents the configuration for a Wintun interface
type Config struct {
	Name    string
	LocalIP string
	MTU     int
	Logger  *logger.Logger
}

// NewInterface creates a new Wintun virtual network interface
func NewInterface(cfg *Config) (*Interface, error) {
	if runtime.GOOS != "windows" {
		return nil, fmt.Errorf("Wintun is only supported on Windows")
	}

	if cfg == nil {
		return nil, fmt.Errorf("config cannot be nil")
	}

	if cfg.Logger == nil {
		return nil, fmt.Errorf("logger cannot be nil")
	}

	// Parse local IP address
	localIP := net.ParseIP(cfg.LocalIP)
	if localIP == nil {
		return nil, fmt.Errorf("invalid local IP address: %s", cfg.LocalIP)
	}

	// Set default MTU if not specified
	mtu := cfg.MTU
	if mtu == 0 {
		mtu = 1500
	}

	iface := &Interface{
		name:     cfg.Name,
		localIP:  localIP,
		mtu:      mtu,
		logger:   cfg.Logger,
		inbound:  make(chan []byte, 256),
		outbound: make(chan []byte, 256),
		stopCh:   make(chan struct{}),
	}

	cfg.Logger.Info("Wintun interface configuration: Name=%s, LocalIP=%s, MTU=%d",
		cfg.Name, cfg.LocalIP, mtu)

	return iface, nil
}

// Start starts the Wintun interface
func (iface *Interface) Start() error {
	iface.mu.Lock()
	defer iface.mu.Unlock()

	if iface.running {
		return fmt.Errorf("interface is already running")
	}

	iface.logger.Info("Starting Wintun interface: %s", iface.name)

	// Create or open the Wintun adapter
	adapter, err := wintun.CreateAdapter(iface.name, "Wintun", nil)
	if err != nil {
		// Try to open existing adapter
		adapter, err = wintun.OpenAdapter(iface.name)
		if err != nil {
			return fmt.Errorf("failed to create/open Wintun adapter: %w", err)
		}
		iface.logger.Info("Opened existing Wintun adapter: %s", iface.name)
	} else {
		iface.logger.Info("Created new Wintun adapter: %s", iface.name)
	}

	iface.adapter = adapter

	// Start a session with the adapter
	session, err := adapter.StartSession(wintun.RingCapacityMin)
	if err != nil {
		adapter.Close()
		return fmt.Errorf("failed to start Wintun session: %w", err)
	}

	iface.session = session
	iface.logger.Info("Started Wintun session")

	// Configure the network interface (IP address, etc.)
	if err := iface.configureInterface(); err != nil {
		iface.cleanup()
		return fmt.Errorf("failed to configure interface: %w", err)
	}

	// Start packet processing goroutines
	iface.wg.Add(2)
	go iface.readLoop()
	go iface.writeLoop()

	iface.running = true
	iface.logger.Info("Wintun interface started successfully")

	return nil
}

// Stop stops the Wintun interface
func (iface *Interface) Stop() error {
	iface.mu.Lock()
	defer iface.mu.Unlock()

	if !iface.running {
		return nil
	}

	iface.logger.Info("Stopping Wintun interface: %s", iface.name)

	// Signal stop
	close(iface.stopCh)

	// Wait for goroutines to finish
	iface.wg.Wait()

	// Cleanup resources
	iface.cleanup()

	iface.running = false
	iface.logger.Info("Wintun interface stopped successfully")

	return nil
}

// cleanup cleans up Wintun resources
func (iface *Interface) cleanup() {
	if iface.session != (wintun.Session{}) {
		iface.session.End()
		iface.logger.Debug("Wintun session ended")
	}

	if iface.adapter != nil {
		if err := iface.adapter.Close(); err != nil {
			iface.logger.Error("Failed to close Wintun adapter: %v", err)
		} else {
			iface.logger.Debug("Wintun adapter closed")
		}
		iface.adapter = nil
	}
}

// configureInterface configures the network interface with IP address and routing
func (iface *Interface) configureInterface() error {
	// Get the adapter's LUID for configuration
	luid := iface.adapter.LUID()
	iface.logger.Debug("Adapter LUID: %d", luid)

	// Configure IP address
	prefixLength := uint8(24) // Default to /24 subnet
	if err := ValidateIPConfiguration(iface.localIP, prefixLength); err != nil {
		return fmt.Errorf("invalid IP configuration: %w", err)
	}

	iface.logger.Info("Configuring interface %s with IP %s/%d", iface.name, iface.localIP.String(), prefixLength)

	if err := ConfigureInterfaceIP(luid, iface.localIP, prefixLength); err != nil {
		iface.logger.Warn("Automatic IP configuration failed: %v", err)
		iface.logger.Info("Attempting alternative configuration methods...")

		// Try alternative configuration method
		if err := iface.configureIPAlternative(); err != nil {
			iface.logger.Error("All IP configuration methods failed: %v", err)
			iface.logger.Info("Please manually configure interface %s with IP %s/%d", iface.name, iface.localIP.String(), prefixLength)
			iface.logger.Info("Command: netsh interface ip set address \"%s\" static %s *************", iface.name, iface.localIP.String())
		} else {
			iface.logger.Info("Alternative IP configuration successful")
		}
	} else {
		iface.logger.Info("Successfully configured IP address %s/%d on interface %s",
			iface.localIP.String(), prefixLength, iface.name)
	}

	// Bring interface up
	if err := SetInterfaceUp(luid); err != nil {
		iface.logger.Warn("Failed to bring interface up: %v", err)
	}

	return nil
}

// readLoop reads packets from the Wintun interface
func (iface *Interface) readLoop() {
	defer iface.wg.Done()
	iface.logger.Debug("Starting Wintun read loop")

	for {
		select {
		case <-iface.stopCh:
			iface.logger.Debug("Stopping Wintun read loop")
			return
		default:
		}

		// Read packet from Wintun
		packet, err := iface.session.ReceivePacket()
		if err != nil {
			// Check if we're shutting down
			select {
			case <-iface.stopCh:
				return
			default:
			}

			iface.logger.Error("Failed to receive packet: %v", err)
			time.Sleep(10 * time.Millisecond)
			continue
		}

		if len(packet) == 0 {
			iface.session.ReleaseReceivePacket(packet)
			continue
		}

		// Make a copy of the packet data
		packetCopy := make([]byte, len(packet))
		copy(packetCopy, packet)

		// Release the original packet
		iface.session.ReleaseReceivePacket(packet)

		// Send to inbound channel
		select {
		case iface.inbound <- packetCopy:
		case <-iface.stopCh:
			return
		default:
			// Drop packet if channel is full
			iface.logger.Warn("Inbound packet dropped - channel full")
		}
	}
}

// writeLoop writes packets to the Wintun interface
func (iface *Interface) writeLoop() {
	defer iface.wg.Done()
	iface.logger.Debug("Starting Wintun write loop")

	for {
		select {
		case <-iface.stopCh:
			iface.logger.Debug("Stopping Wintun write loop")
			return
		case packet := <-iface.outbound:
			if err := iface.writePacket(packet); err != nil {
				iface.logger.Error("Failed to write packet: %v", err)
			}
		}
	}
}

// writePacket writes a single packet to the Wintun interface
func (iface *Interface) writePacket(packet []byte) error {
	if len(packet) == 0 {
		return fmt.Errorf("empty packet")
	}

	// Allocate send packet
	sendPacket, err := iface.session.AllocateSendPacket(len(packet))
	if err != nil {
		return fmt.Errorf("failed to allocate send packet: %w", err)
	}

	// Copy data to send packet
	copy(sendPacket, packet)

	// Send the packet
	iface.session.SendPacket(sendPacket)

	return nil
}

// ReadPacket reads a packet from the interface (blocking)
func (iface *Interface) ReadPacket() ([]byte, error) {
	select {
	case packet := <-iface.inbound:
		return packet, nil
	case <-iface.stopCh:
		return nil, fmt.Errorf("interface stopped")
	}
}

// WritePacket writes a packet to the interface
func (iface *Interface) WritePacket(packet []byte) error {
	select {
	case iface.outbound <- packet:
		return nil
	case <-iface.stopCh:
		return fmt.Errorf("interface stopped")
	default:
		return fmt.Errorf("outbound channel full")
	}
}

// IsRunning returns whether the interface is currently running
func (iface *Interface) IsRunning() bool {
	iface.mu.RLock()
	defer iface.mu.RUnlock()
	return iface.running
}

// GetName returns the interface name
func (iface *Interface) GetName() string {
	return iface.name
}

// GetLocalIP returns the local IP address
func (iface *Interface) GetLocalIP() net.IP {
	return iface.localIP
}

// GetMTU returns the MTU
func (iface *Interface) GetMTU() int {
	return iface.mtu
}

// configureIPAlternative tries alternative IP configuration methods
func (iface *Interface) configureIPAlternative() error {
	// Wait a moment for the interface to be fully ready
	time.Sleep(2 * time.Second)

	// Try using netsh command directly
	return configureInterfaceIPNetsh(iface.adapter.LUID(), iface.localIP, 24)
}
