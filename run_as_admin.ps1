# Run TProxy as Administrator
# This script will start the TProxy application with administrator privileges

# Get the directory of this script
$scriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
Set-Location -Path $scriptDir

Write-Host "Running TProxy with administrator privileges..." -ForegroundColor Green
Write-Host "Current directory: $scriptDir" -ForegroundColor Yellow

# Run the cargo command with administrator privileges
try {
    Start-Process -FilePath "cargo" -ArgumentList "run" -Verb RunAs -Wait
    Write-Host "TProxy execution completed." -ForegroundColor Green
} catch {
    Write-Host "Error running TProxy: $_" -ForegroundColor Red
}

Write-Host "Press any key to exit..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")