# TProxy - IPv4 to IPv6 Traffic Proxy

TProxy is a Rust-based transparent proxy that intercepts IPv4 traffic on a WinTun virtual network interface, converts it to IPv6, and forwards it to the local Tailscale network interface. This solves subnet overlap issues and enables access using original device IPs.

## Features

- **Traffic Interception**: Uses WinDivert to capture IPv4 packets on a WinTun virtual interface
- **Protocol Translation**: Converts IPv4 packets to IPv6 with proper address mapping
- **TCP Connection State Management**: Tracks TCP connection states for proper bidirectional communication
- **Configuration Management**: Supports comprehensive configuration via YAML file
- **Traffic Filtering**: Only processes packets matching configured IP ranges for performance optimization
- **Bidirectional Communication**: Handles return traffic routing with proper address translation

## Architecture

```
[IPv4 Client] -> [WinTun Interface] -> [TProxy] -> [IPv6 Conversion] -> [Tailscale Interface] -> [Remote IPv6 Network]
                                          ^                                      |
                                          |                                      v
                                    [Connection Tracking]              [Return Traffic]
                                          ^                                      |
                                          |                                      v
[IPv4 Client] <- [Address Translation] <- [IPv6 to IPv4 Conversion] <- [Tailscale Interface]
```

## Prerequisites

- Windows operating system
- Administrator privileges (required for WinTun and WinDivert)
- Rust development environment
- Tailscale client installed and running

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd tproxy
```

2. Build the project:
```bash
cargo build --release
```

## Configuration

Edit `config.yaml` to configure your network setup:

```yaml
# Network Interface Configuration
interfaces:
  wintun:
    name: "TProxy-Wintun"
    ipv4_address: "*************"
    ipv4_netmask: "*************"
    ipv6_address: "fd00::1"
  tailscale:
    # Tailscale interface will be auto-detected
    # interface_index: 12  # Optional: specify if needed

# IPv4 to IPv6 Address Mappings
address_mappings:
  "*************": "fe80::ca75:f4ff:fe67:c094"
  "*************": "2001:db8::101"
  "*************": "2001:db8::100"

# Traffic Filtering Rules
traffic_filters:
  ipv4_subnets:
    - "*************/24"
    - "***********/24"
  ipv6_subnets:
    - "fe80::/64"
    - "2001:db8::/32"

# Connection Tracking Settings
connection_tracking:
  timeout_seconds: 300
  max_connections: 10000
  cleanup_interval_seconds: 60

# Logging Configuration
logging:
  level: "info"
  log_packets: false
  log_conversions: true
```

## Usage

### Running the Proxy

1. **As Administrator** (required):
```bash
# Using the provided batch file
run_as_admin.bat

# Or manually with PowerShell
powershell -Command "Start-Process cargo -ArgumentList 'run' -Verb RunAs"
```

2. **With Debug Logging**:
```bash
run_as_admin_debug.bat
```

3. **With Info Logging**:
```bash
run_with_info.bat
```

### Example Scenario

1. Configure your IPv4 devices to use `*************` as their gateway
2. Add address mappings in `config.yaml` for each device
3. Start TProxy with administrator privileges
4. IPv4 traffic will be converted to IPv6 and forwarded through Tailscale

## Technical Details

### Packet Processing Flow

1. **Capture**: WinDivert captures packets on the WinTun interface
2. **Filter**: Traffic filters determine if packet should be processed
3. **Parse**: Packet headers are parsed using etherparse
4. **Track**: TCP connections are tracked for state management
5. **Convert**: IPv4 packets are converted to IPv6 with proper checksums
6. **Forward**: Converted packets are sent to the Tailscale interface
7. **Return**: IPv6 return traffic is converted back to IPv4

### Connection Tracking

- Maintains TCP connection states (SYN_SENT, ESTABLISHED, etc.)
- Tracks bidirectional address mappings
- Automatic cleanup of expired connections
- Supports both TCP and UDP protocols

### Address Translation

- IPv4 addresses are mapped to specific IPv6 addresses via configuration
- Return traffic uses IPv4-mapped IPv6 addresses for compatibility
- Proper checksum recalculation for both IPv4 and IPv6 headers
- Port numbers are preserved during translation

## Testing

Run the test suite to validate functionality:

```bash
cargo test
```

Tests cover:
- TCP connection tracking
- Traffic filtering
- Checksum calculation
- Configuration loading
- Packet conversion

## Troubleshooting

### Common Issues

1. **"Failed to load wintun.dll"**
   - Ensure `wintun.dll` is in the `lib/` directory
   - Run as Administrator

2. **"Failed to open WinDivert handle"**
   - Ensure you're running as Administrator
   - Check that WinDivert drivers are properly installed

3. **"Failed to create Wintun adapter"**
   - Run as Administrator
   - Ensure no conflicting network adapters exist

### Logging

Enable debug logging for detailed packet information:
```bash
set RUST_LOG=debug
cargo run
```

## Development

### Project Structure

- `src/main.rs` - Main application logic
- `src/tests.rs` - Test suite
- `config.yaml` - Configuration file
- `lib/` - Required DLL files (WinDivert, WinTun)

### Key Components

- **Connection Tracking**: Manages TCP connection states and mappings
- **Packet Conversion**: Handles IPv4 ↔ IPv6 protocol translation
- **Traffic Filtering**: Optimizes performance by filtering relevant packets
- **Configuration Management**: Flexible YAML-based configuration

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]
