@echo off
echo ========================================
echo IPv6 Routing and Connectivity Setup
echo ========================================
echo.
echo CRITICAL ISSUE: Router is responding to fe80::16d5:6ea1:e8d6:3fd2 instead of fe80::1234:5678:9abc:def0
echo This means the system is changing our source address when sending IPv6 packets!
echo.
echo Our WinTun IPv6: fe80::1234:5678:9abc:def0
echo Router IPv6:     fe80::ca75:f4ff:fe67:c094
echo System thinks:   fe80::16d5:6ea1:e8d6:3fd2
echo.

echo Current IPv6 addresses:
netsh interface ipv6 show addresses
echo.

echo Current IPv6 routes:
netsh interface ipv6 show route
echo.

echo Adding specific route for router via WinTun interface...
netsh interface ipv6 add route fe80::ca75:f4ff:fe67:c094/128 "TProxy-Wintun" fe80::1234:5678:9abc:def0

echo.
echo Testing connectivity from WinTun interface...
ping -6 -S fe80::1234:5678:9abc:def0 fe80::ca75:f4ff:fe67:c094 -n 2

echo.
echo Updated IPv6 routes:
netsh interface ipv6 show route

echo.
echo IPv6 neighbor table:
netsh interface ipv6 show neighbors

echo.
echo ========================================
echo IPv6 routing setup completed!
echo If ping worked, the routing should be fixed.
echo ========================================
pause
