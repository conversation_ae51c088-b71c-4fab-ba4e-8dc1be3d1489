# TProxy Usage Guide

## Quick Start

### 1. Setup Environment

Ensure you have:
- Windows 10/11 with Administrator privileges
- Rust installed (`rustup` recommended)
- Tailscale client running

### 2. Configure Address Mappings

Edit `config.yaml` to map your IPv4 devices to IPv6 addresses:

```yaml
address_mappings:
  "*************": "2001:db8::100"  # Device 1
  "*************": "2001:db8::101"  # Device 2
  "*************": "2001:db8::102"  # Device 3
```

### 3. Run TProxy

```bash
# Build and run as administrator
run_as_admin.bat
```

### 4. Configure Client Devices

Set the gateway on your IPv4 devices to point to the WinTun interface:
- Gateway: `*************`
- DNS: Your preferred DNS servers

## Advanced Configuration

### Custom Interface Settings

```yaml
interfaces:
  wintun:
    name: "MyProxy-Wintun"
    ipv4_address: "**********"
    ipv4_netmask: "*************"
    ipv6_address: "fd00:100::1"
```

### Traffic Filtering for Performance

```yaml
traffic_filters:
  ipv4_subnets:
    - "***********/24"    # Only process this subnet
    - "10.0.0.0/16"       # And this range
  ipv6_subnets:
    - "2001:db8::/32"     # Target IPv6 network
```

### Connection Tracking Tuning

```yaml
connection_tracking:
  timeout_seconds: 600        # 10 minutes
  max_connections: 50000      # Higher limit
  cleanup_interval_seconds: 30 # More frequent cleanup
```

## Monitoring and Debugging

### Enable Debug Logging

```bash
set RUST_LOG=debug
run_as_admin_debug.bat
```

### Monitor Traffic

The application logs all packet conversions:
```
INFO  [tproxy] TCP IPv4->IPv6: *************:12345 -> *************:80
INFO  [tproxy] Packet processed and converted successfully
```

### Check Connection Status

Debug logs show connection tracking:
```
DEBUG [tproxy] Tracked TCP connection: ConnectionKey { ... } -> SynSent
```

## Example Scenarios

### Scenario 1: Home Network Integration

**Problem**: Home devices on ***********/24 need to access Tailscale network

**Solution**:
1. Configure TProxy with WinTun on *************
2. Map each device to unique IPv6 address
3. Set device gateways to *************
4. Traffic flows: Device -> WinTun -> TProxy -> IPv6 -> Tailscale

### Scenario 2: Industrial IoT Integration

**Problem**: Legacy IoT devices with fixed IPs need cloud access

**Solution**:
1. Use TProxy to bridge legacy IPv4 devices to modern IPv6 infrastructure
2. Maintain original device IPs for compatibility
3. Enable secure access through Tailscale mesh network

## Performance Optimization

### 1. Traffic Filtering
- Configure specific subnets to reduce processing overhead
- Use narrow IP ranges in `traffic_filters`

### 2. Connection Limits
- Adjust `max_connections` based on expected load
- Tune `cleanup_interval_seconds` for your use case

### 3. Logging
- Set `log_packets: false` in production
- Use `info` level logging for normal operation

## Troubleshooting

### Network Interface Issues

1. **Check WinTun Adapter**:
```bash
ipconfig /all
# Look for "TProxy-Wintun" adapter
```

2. **Verify IPv6 Configuration**:
```bash
netsh interface ipv6 show addresses
```

3. **Check Tailscale Status**:
```bash
tailscale status
```

### Packet Flow Issues

1. **Enable Packet Logging**:
```yaml
logging:
  log_packets: true
  log_conversions: true
```

2. **Check Address Mappings**:
- Verify IPv4 addresses in config match your devices
- Ensure IPv6 addresses are reachable via Tailscale

3. **Monitor Connection Tracking**:
- Check debug logs for connection state changes
- Verify TCP handshake completion

### Performance Issues

1. **Reduce Traffic Filters**:
- Use more specific subnet filters
- Exclude unnecessary traffic

2. **Adjust Connection Tracking**:
- Increase cleanup interval for high-traffic scenarios
- Reduce timeout for short-lived connections

## Security Considerations

- Run with minimal required privileges
- Regularly update WinDivert and WinTun components
- Monitor for unusual traffic patterns
- Use secure IPv6 addresses in mappings

## Integration with Tailscale

### Best Practices

1. **IPv6 Address Planning**:
   - Use consistent IPv6 prefix for mapped addresses
   - Document address mappings for team reference

2. **Tailscale Configuration**:
   - Ensure IPv6 is enabled in Tailscale
   - Configure appropriate ACLs for mapped addresses

3. **Monitoring**:
   - Use Tailscale admin console to monitor traffic
   - Set up alerts for connection issues

## Limitations

- Windows-only (due to WinTun/WinDivert dependencies)
- Requires Administrator privileges
- IPv4-mapped IPv6 addresses for return traffic
- TCP and UDP protocols supported (ICMP not implemented)

## Future Enhancements

- ICMP protocol support
- Dynamic address mapping
- Web-based configuration interface
- Integration with other VPN solutions
- Linux/macOS support (using different packet capture methods)
