# TProxy Configuration File
# This file configures the IPv4 to IPv6 traffic proxy

# Network Interface Configuration
interfaces:
  wintun:
    name: "TProxy-Wintun"
    ipv4_address: "*************"
    ipv4_netmask: "*************"
    ipv6_address: "fd00::1"
  tailscale:
    # Tailscale interface will be auto-detected
    # You can specify interface index if needed
    # interface_index: 12

# IPv4 to IPv6 Address Mappings
# Format: ipv4_address -> ipv6_address
address_mappings:
  # 使用真实可达的IPv6地址 - 你确认这个地址可以通过浏览器访问
  "*************": "fe80::ca75:f4ff:fe67:c094"  # 路由器的IPv6地址
  "*************": "2001:db8::101"
  #"*************": "2001:db8::100"
  #"*************": "2001:db8::101"

# Traffic Filtering Rules
# Only process packets matching these IP ranges for performance
traffic_filters:
  ipv4_subnets:
    - "*************/24"
    - "0.0.0.0/0"  # 临时允许所有IPv4流量用于调试
    #- "***********/24"
  ipv6_subnets:
    - "::/0"  # 临时允许所有IPv6流量用于调试
    #- "fe80::/64"
    #- "2001:db8::/32"

# Connection Tracking Settings
connection_tracking:
  timeout_seconds: 300  # 5 minutes
  max_connections: 10000
  cleanup_interval_seconds: 60

# Logging Configuration
logging:
  level: "info"  # debug, info, warn, error
  log_packets: false
  log_conversions: true