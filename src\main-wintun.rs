use std::sync::Arc;

fn main() -> Result<(), Box<dyn std::error::Error>> {
    env_logger::init();
    
    println!("Loading wintun.dll...");
    
    // Load the wintun dll file
    let wintun = unsafe { wintun::load() }.expect("Failed to load wintun dll");

    // Try to open an adapter with the name "TProxy-Wintun"
    let adapter = match wintun::Adapter::open(&wintun, "TProxy-Wintun") {
        Ok(a) => {
            println!("Opened existing adapter");
            a
        },
        Err(_) => {
            // If loading failed (most likely it didn't exist), create a new one
            println!("Creating new adapter");
            wintun::Adapter::create(&wintun, "TProxy-Wintun", "Wintun Adapter for TProxy", None)?
        }
    };

    // Get the adapter index
    let adapter_index = adapter.get_adapter_index()?;
    println!("Adapter index: {}", adapter_index);
    
    // Configure the adapter with IP address
    let ip_addr = std::net::Ipv4Addr::new(192, 168, 200, 1);
    let netmask = std::net::Ipv4Addr::new(255, 255, 255, 0);
    let gateway = std::net::Ipv4Addr::new(192, 168, 200, 1);
    
    println!("Setting network addresses...");
    adapter.set_network_addresses_tuple(
        std::net::IpAddr::V4(ip_addr),
        std::net::IpAddr::V4(netmask),
        Some(std::net::IpAddr::V4(gateway))
    )?;
    
    println!("Successfully configured adapter with IP: {}, Netmask: {}, Gateway: {}", 
             ip_addr, netmask, gateway);
    
    // Start a session with the adapter
    println!("Starting session...");
    let session = Arc::new(adapter.start_session(wintun::MAX_RING_CAPACITY)?);
    println!("Started session successfully");

    // Keep the adapter alive for a while
    println!("Sleeping for 30 seconds...");
    std::thread::sleep(std::time::Duration::from_secs(30));
    
    println!("Test completed");
    Ok(())
}