@echo off
echo ========================================
echo IPv6 Address Conflict Fix
echo ========================================
echo.
echo Problem: Both Tailscale and TProxy-<PERSON>tun have the same IPv6 address
echo Solution: Remove conflicting IPv6 address from TProxy-Wintun and add a unique one
echo.

echo Current IPv6 addresses:
netsh interface ipv6 show addresses
echo.

echo Removing conflicting IPv6 address from TProxy-Wintun...
netsh interface ipv6 delete address "TProxy-Wintun" fe80::71c5:bdb2:3695:1897
echo.

echo Adding unique IPv6 address to TProxy-Wintun...
netsh interface ipv6 add address "TProxy-Wintun" fe80::1234:5678:9abc:def0
echo.

echo Updated IPv6 addresses:
netsh interface ipv6 show addresses
echo.

echo IPv6 routing table:
netsh interface ipv6 show route
echo.

echo ========================================
echo Fix completed! Now TProxy-<PERSON><PERSON> has a unique IPv6 address.
echo This should resolve the routing conflict.
echo ========================================
pause
